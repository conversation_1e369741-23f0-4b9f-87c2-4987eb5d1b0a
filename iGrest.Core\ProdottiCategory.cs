//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class ProdottiCategory
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public ProdottiCategory()
        {
            this.Prodotti = new HashSet<Prodotti>();
        }
    
        public System.Guid ID_ProdottoCategory { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public System.Guid ID_Grest { get; set; }
        public bool Enabled { get; set; }
        public Nullable<int> ID_ProdottoTipo { get; set; }
    
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Prodotti> Prodotti { get; set; }
        public virtual ProdottiTipo ProdottiTipo { get; set; }
    }
}
