﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="7.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="7.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Serilog" version="2.12.0" targetFramework="net48" />
  <package id="Serilog.Extensions.Logging" version="3.1.0" targetFramework="net48" />
  <package id="Serilog.Sinks.File" version="5.0.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="7.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.5.5" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>