//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Triage
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Triage()
        {
            this.Triage_InputOutput = new HashSet<Triage_InputOutput>();
        }
    
        public int ID_Triage { get; set; }
        public string Name { get; set; }
        public bool IsMandatory { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_TriageCategoria { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Triage_Categorie Triage_Categorie { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Triage_InputOutput> Triage_InputOutput { get; set; }
    }
}
