﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace iGrest.Utils
{
	/// <summary>
	/// Interaction logic for MainWindow.xaml
	/// </summary>
	public partial class MainWindow : Window
	{
		public MainWindow()
		{
			InitializeComponent();
		}

		private void manageCurrentWindow()
		{
			
		}

		private void Search_Click(object sender, RoutedEventArgs e)
		{
			manageCurrentWindow();

			var form = new SearchWindow();

			form.Show();
		}

		private void Deploy_Click(object sender, RoutedEventArgs e)
		{
			manageCurrentWindow();

			var form = new DeployWindow();

			form.Show();
		}
	}
}
