//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class SubscriptionAttachments
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public SubscriptionAttachments()
        {
            this.Rel_User_SubscriptionAttachments = new HashSet<Rel_User_SubscriptionAttachments>();
        }
    
        public long ID_SubscriptionAttachment { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsMandatory { get; set; }
        public System.Guid ID_Grest { get; set; }
        public bool Enabled { get; set; }
        public bool Removed { get; set; }
        public Nullable<System.DateTime> RemovedDate { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public string FileName { get; set; }
    
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_User_SubscriptionAttachments> Rel_User_SubscriptionAttachments { get; set; }
    }
}
