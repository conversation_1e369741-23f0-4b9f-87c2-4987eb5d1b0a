<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8B37F093-13AC-405E-82B4-43CCD97A207E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>iGrest.Core</RootNamespace>
    <AssemblyName>iGrest.Core</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Activity.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="BalanceIncomeStatements.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="BalanceReceiptDetails.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="BalanceSheets.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="BankTransferConfig.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Bar.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Comuni.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Configurations.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Dinners.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="FE_CondizioniPagamento.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="FE_FormatoTrasmissione.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="FE_ModalitaPagamento.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="FE_RegimeFiscale.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="FE_TipoDocumento.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Fornitori.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="GrestFatt.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="GrestHeadquarters.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="GrestMenu.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="GrestMenuCustom.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Document.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Files.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_FilesType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Files_Users.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Iscrizione_Type.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Progressivo_Ricevuta.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_UserAdditionalDetails.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Grest_Year.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Groups.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="iGrestDBModel.Context.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>iGrestDBModel.Context.tt</DependentUpon>
    </Compile>
    <Compile Include="iGrestDBModel.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="iGrestDBModel.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>iGrestDBModel.edmx</DependentUpon>
    </Compile>
    <Compile Include="iGrestDBModel1.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="InputOutput.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Listino.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Log.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Logs.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Mensa.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="MessageRead.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Messages.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="News.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="News_Read.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="NotificType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Order.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="OrderItem.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Pagamenti.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Partials\Groups.cs" />
    <Compile Include="Partials\Trips.cs" />
    <Compile Include="PaymentType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="PayPalConfig.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Portale.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="PrintableObject.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Prodotti.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ProdottiCategory.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="ProdottiTipo.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Province.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Regioni.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Files_Fornitori.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Files_Groups.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_UsersType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Activity.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Groups.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Headquarter.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_News.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="REL_Users_NotificPlayers.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Training.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Trip.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_TripsPrice.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Trip_Bus.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_Users_Year.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Rel_User_SubscriptionAttachments.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="SatispayConfig.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="SatispayTransaction.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="SchoolClass.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Sizes.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Split_Result.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="StripeConfig.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="StripeTransaction.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="SubscriptionAttachments.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Temp_Table.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Trainings.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TransactionType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Triage.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Triage_Categorie.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Triage_InputOutput.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Trips.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TripsDiscountConditions.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TripsMaster.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TripsPrice.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="TripType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Trip_Bus.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Users.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="Users_AdditionalDetails.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="UserToken.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="UserType.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
    <Compile Include="W_BalanceUsers.cs">
      <DependentUpon>iGrestDBModel.tt</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.Config">
      <SubType>Designer</SubType>
    </None>
    <EntityDeploy Include="iGrestDBModel.edmx">
      <Generator>EntityModelCodeGenerator</Generator>
      <LastGenOutput>iGrestDBModel.Designer.cs</LastGenOutput>
    </EntityDeploy>
    <None Include="iGrestDBModel.edmx.diagram">
      <DependentUpon>iGrestDBModel.edmx</DependentUpon>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="iGrestDBModel.Context.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>iGrestDBModel.edmx</DependentUpon>
      <LastGenOutput>iGrestDBModel.Context.cs</LastGenOutput>
    </Content>
    <Content Include="iGrestDBModel.edmx.sql" />
    <Content Include="iGrestDBModel.tt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <DependentUpon>iGrestDBModel.edmx</DependentUpon>
      <LastGenOutput>iGrestDBModel1.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>