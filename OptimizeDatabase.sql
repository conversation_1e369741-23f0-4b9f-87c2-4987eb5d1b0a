-- =====================================================
-- SCRIPT DI OTTIMIZZAZIONE DATABASE iGrest
-- Data: 07/06/2025
-- Scopo: Ottimizzare database rimuovendo log vecchi e riducendo file
-- =====================================================

USE iGrest;
GO

PRINT '=== OTTIMIZZAZIONE DATABASE iGrest ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. ANALISI SITUAZIONE ATTUALE
-- =====================================================

PRINT '1. SITUAZIONE ATTUALE'
PRINT '---------------------'

-- Conteggio record attuali
SELECT 'Logs' as Tabella, COUNT(*) as Record FROM Logs
UNION ALL
SELECT 'Log' as Tabella, COUNT(*) as Record FROM Log;

-- Dimensioni file attuali
SELECT 
    name as NomeFile,
    type_desc as Tipo,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as UsatoMB
FROM sys.database_files;

PRINT ''

-- =====================================================
-- 2. ANALISI ETÀ DEI LOG
-- =====================================================

PRINT '2. ANALISI ETÀ DEI LOG'
PRINT '----------------------'

-- Analisi tabella Logs (usa DataInsert)
PRINT 'Tabella Logs - Distribuzione per età:'
SELECT 
    CASE 
        WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END as Periodo,
    COUNT(*) as NumeroRecord,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Logs) as Percentuale
FROM Logs 
WHERE DataInsert IS NOT NULL
GROUP BY 
    CASE 
        WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END
ORDER BY 
    CASE 
        WHEN CASE 
            WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 7 giorni' THEN 1
        WHEN CASE 
            WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 30 giorni' THEN 2
        WHEN CASE 
            WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 90 giorni' THEN 3
        WHEN CASE 
            WHEN DataInsert >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DataInsert >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DataInsert >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DataInsert >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimo anno' THEN 4
        ELSE 5
    END;

PRINT ''

-- Analisi tabella Log (usa DTINSERT)
PRINT 'Tabella Log - Distribuzione per età:'
SELECT 
    CASE 
        WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END as Periodo,
    COUNT(*) as NumeroRecord,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Log) as Percentuale
FROM Log 
WHERE DTINSERT IS NOT NULL
GROUP BY 
    CASE 
        WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END
ORDER BY 
    CASE 
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 7 giorni' THEN 1
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 30 giorni' THEN 2
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 90 giorni' THEN 3
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimo anno' THEN 4
        ELSE 5
    END;

PRINT ''

-- =====================================================
-- 3. PULIZIA LOG VECCHI (CONSERVA ULTIMI 90 GIORNI)
-- =====================================================

PRINT '3. PULIZIA LOG VECCHI'
PRINT '---------------------'

-- Backup dei dati che verranno eliminati (opzionale)
PRINT 'Creazione backup log da eliminare...'

-- Conta quanti record verranno eliminati
DECLARE @LogsToDelete INT, @LogToDelete INT

SELECT @LogsToDelete = COUNT(*) FROM Logs WHERE DataInsert < DATEADD(day, -90, GETDATE())
SELECT @LogToDelete = COUNT(*) FROM Log WHERE DTINSERT < DATEADD(day, -90, GETDATE())

PRINT 'Record da eliminare:'
PRINT '  - Tabella Logs: ' + CAST(@LogsToDelete AS VARCHAR) + ' record'
PRINT '  - Tabella Log: ' + CAST(@LogToDelete AS VARCHAR) + ' record'

-- Elimina log più vecchi di 90 giorni dalla tabella Logs
PRINT 'Eliminazione log vecchi dalla tabella Logs...'
DELETE FROM Logs WHERE DataInsert < DATEADD(day, -90, GETDATE())
PRINT '✓ Eliminati ' + CAST(@@ROWCOUNT AS VARCHAR) + ' record dalla tabella Logs'

-- Elimina log più vecchi di 90 giorni dalla tabella Log
PRINT 'Eliminazione log vecchi dalla tabella Log...'
DELETE FROM Log WHERE DTINSERT < DATEADD(day, -90, GETDATE())
PRINT '✓ Eliminati ' + CAST(@@ROWCOUNT AS VARCHAR) + ' record dalla tabella Log'

PRINT ''

-- =====================================================
-- 4. OTTIMIZZAZIONE INDICI E SPAZIO
-- =====================================================

PRINT '4. OTTIMIZZAZIONE INDICI E SPAZIO'
PRINT '----------------------------------'

-- Riorganizza indici tabella Logs
PRINT 'Riorganizzazione indici tabella Logs...'
ALTER INDEX ALL ON Logs REORGANIZE
PRINT '✓ Indici tabella Logs riorganizzati'

-- Riorganizza indici tabella Log
PRINT 'Riorganizzazione indici tabella Log...'
ALTER INDEX ALL ON Log REORGANIZE
PRINT '✓ Indici tabella Log riorganizzati'

-- Aggiorna statistiche
PRINT 'Aggiornamento statistiche...'
UPDATE STATISTICS Logs
UPDATE STATISTICS Log
PRINT '✓ Statistiche aggiornate'

PRINT ''

-- =====================================================
-- 5. RIDUZIONE FILE DATABASE
-- =====================================================

PRINT '5. RIDUZIONE FILE DATABASE'
PRINT '--------------------------'

-- Shrink del database
PRINT 'Riduzione dimensioni database...'
DBCC SHRINKDATABASE(iGrest, 10)
PRINT '✓ Database ridotto'

-- Shrink specifico del file di log
PRINT 'Riduzione file di log...'
DBCC SHRINKFILE('iGrest_log', 50)
PRINT '✓ File di log ridotto'

PRINT ''

-- =====================================================
-- 6. RISULTATI FINALI
-- =====================================================

PRINT '6. RISULTATI FINALI'
PRINT '-------------------'

-- Conteggio record finali
PRINT 'Record rimanenti:'
SELECT 'Logs' as Tabella, COUNT(*) as Record FROM Logs
UNION ALL
SELECT 'Log' as Tabella, COUNT(*) as Record FROM Log;

-- Dimensioni file finali
PRINT 'Dimensioni file finali:'
SELECT 
    name as NomeFile,
    type_desc as Tipo,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as UsatoMB,
    CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024 AS DECIMAL(15,2)) as LiberoMB
FROM sys.database_files;

PRINT ''
PRINT '=== OTTIMIZZAZIONE COMPLETATA ==='
PRINT 'RACCOMANDAZIONI:'
PRINT '• Eseguire questa pulizia mensilmente'
PRINT '• Considerare l''implementazione di un job automatico per la pulizia'
PRINT '• Monitorare la crescita delle tabelle di log'
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)