<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
  </configSections>
  <connectionStrings>
    <!-- Local connection strings for development -->
    <add name="iGrestEntities" connectionString="metadata=res://*/iGrestDBModel.csdl|res://*/iGrestDBModel.ssdl|res://*/iGrestDBModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;Data Source=.\SQLEXPRESS,1433;Initial Catalog=iGrest;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True&quot;" providerName="System.Data.EntityClient"/>
    <!-- Standard SQL connection string -->
    <add name="ConnectionString" connectionString="Data Source=.\SQLEXPRESS,1433;Initial Catalog=iGrest;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
  </connectionStrings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb"/>
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8"/></startup></configuration>

