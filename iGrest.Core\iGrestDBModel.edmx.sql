
-- --------------------------------------------------
-- En<PERSON><PERSON> Designer DDL Script for SQL Server 2005, 2008, 2012 and Azure
-- --------------------------------------------------
-- Date Created: 11/29/2019 21:31:58
-- Generated from EDMX file: D:\<PERSON>getti\AlienPro\iGrest\Source\iGrest.Core\iGrestDBModel.edmx
-- --------------------------------------------------

SET QUOTED_IDENTIFIER OFF;
GO
USE [iGrest.it.live.test];
GO
IF SCHEMA_ID(N'dbo') IS NULL EXECUTE(N'CREATE SCHEMA [dbo]');
GO

-- --------------------------------------------------
-- Dropping existing FOREIGN KEY constraints
-- --------------------------------------------------

IF OBJECT_ID(N'[dbo].[FK_PayPalConfig_Grest]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[PayPalConfig] DROP CONSTRAINT [FK_PayPalConfig_Grest];
GO
IF OBJECT_ID(N'[dbo].[FK_Rel_Users_Year_Year]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Rel_Users_Year] DROP CONSTRAINT [FK_Rel_Users_Year_Year];
GO
IF OBJECT_ID(N'[dbo].[FK_Rel_Users_News_News]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Rel_Users_News] DROP CONSTRAINT [FK_Rel_Users_News_News];
GO
IF OBJECT_ID(N'[dbo].[FK_Rel_Users_Year_Users]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Rel_Users_Year] DROP CONSTRAINT [FK_Rel_Users_Year_Users];
GO
IF OBJECT_ID(N'[dbo].[FK_Rel_Users_Year_UserType]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Rel_Users_Year] DROP CONSTRAINT [FK_Rel_Users_Year_UserType];
GO
IF OBJECT_ID(N'[dbo].[FK_Bar_Bar]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Bar] DROP CONSTRAINT [FK_Bar_Bar];
GO
IF OBJECT_ID(N'[dbo].[FK_Grest_UserAdditionalDetails_Grest]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Grest_UserAdditionalDetails] DROP CONSTRAINT [FK_Grest_UserAdditionalDetails_Grest];
GO
IF OBJECT_ID(N'[dbo].[FK_GrestFatt_Grest]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[GrestFatt] DROP CONSTRAINT [FK_GrestFatt_Grest];
GO
IF OBJECT_ID(N'[dbo].[FK_Grest_Files_Grest_FilesType]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Grest_Files] DROP CONSTRAINT [FK_Grest_Files_Grest_FilesType];
GO
IF OBJECT_ID(N'[dbo].[FK_Listino_Listino]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Listino] DROP CONSTRAINT [FK_Listino_Listino];
GO
IF OBJECT_ID(N'[dbo].[FK_Pagamenti_Users]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Pagamenti] DROP CONSTRAINT [FK_Pagamenti_Users];
GO
IF OBJECT_ID(N'[dbo].[FK_Rel_Users_News_Users]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Rel_Users_News] DROP CONSTRAINT [FK_Rel_Users_News_Users];
GO
IF OBJECT_ID(N'[dbo].[FK_Trips_TripType]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Trips] DROP CONSTRAINT [FK_Trips_TripType];
GO
IF OBJECT_ID(N'[dbo].[FK_Users_AdditionalDetails_Users]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[Users_AdditionalDetails] DROP CONSTRAINT [FK_Users_AdditionalDetails_Users];
GO
IF OBJECT_ID(N'[dbo].[FK_REL_Users_NotificPlayers_NotificType]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[REL_Users_NotificPlayers] DROP CONSTRAINT [FK_REL_Users_NotificPlayers_NotificType];
GO
IF OBJECT_ID(N'[dbo].[FK_REL_Users_NotificPlayers_Users]', 'F') IS NOT NULL
    ALTER TABLE [dbo].[REL_Users_NotificPlayers] DROP CONSTRAINT [FK_REL_Users_NotificPlayers_Users];
GO

-- --------------------------------------------------
-- Dropping existing tables
-- --------------------------------------------------

IF OBJECT_ID(N'[dbo].[Activity]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Activity];
GO
IF OBJECT_ID(N'[dbo].[BalanceSheets]', 'U') IS NOT NULL
    DROP TABLE [dbo].[BalanceSheets];
GO
IF OBJECT_ID(N'[dbo].[Bar]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Bar];
GO
IF OBJECT_ID(N'[dbo].[Comuni]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Comuni];
GO
IF OBJECT_ID(N'[dbo].[Dinners]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Dinners];
GO
IF OBJECT_ID(N'[dbo].[Fornitori]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Fornitori];
GO
IF OBJECT_ID(N'[dbo].[Grest]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest];
GO
IF OBJECT_ID(N'[dbo].[Grest_Files]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_Files];
GO
IF OBJECT_ID(N'[dbo].[Grest_FilesType]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_FilesType];
GO
IF OBJECT_ID(N'[dbo].[Grest_Iscrizione_Type]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_Iscrizione_Type];
GO
IF OBJECT_ID(N'[dbo].[Grest_Progressivo_Ricevuta]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_Progressivo_Ricevuta];
GO
IF OBJECT_ID(N'[dbo].[Grest_UserAdditionalDetails]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_UserAdditionalDetails];
GO
IF OBJECT_ID(N'[dbo].[Grest_Year]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Grest_Year];
GO
IF OBJECT_ID(N'[dbo].[GrestFatt]', 'U') IS NOT NULL
    DROP TABLE [dbo].[GrestFatt];
GO
IF OBJECT_ID(N'[dbo].[Groups]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Groups];
GO
IF OBJECT_ID(N'[dbo].[InputOutput]', 'U') IS NOT NULL
    DROP TABLE [dbo].[InputOutput];
GO
IF OBJECT_ID(N'[dbo].[Listino]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Listino];
GO
IF OBJECT_ID(N'[dbo].[Log]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Log];
GO
IF OBJECT_ID(N'[dbo].[Logs]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Logs];
GO
IF OBJECT_ID(N'[dbo].[Mensa]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Mensa];
GO
IF OBJECT_ID(N'[dbo].[News]', 'U') IS NOT NULL
    DROP TABLE [dbo].[News];
GO
IF OBJECT_ID(N'[dbo].[Pagamenti]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Pagamenti];
GO
IF OBJECT_ID(N'[dbo].[PayPalConfig]', 'U') IS NOT NULL
    DROP TABLE [dbo].[PayPalConfig];
GO
IF OBJECT_ID(N'[dbo].[Prodotti]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Prodotti];
GO
IF OBJECT_ID(N'[dbo].[Province]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Province];
GO
IF OBJECT_ID(N'[dbo].[Regioni]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Regioni];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_Activity]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_Activity];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_Groups]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_Groups];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_News]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_News];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_Trip]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_Trip];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_Trip_Bus]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_Trip_Bus];
GO
IF OBJECT_ID(N'[dbo].[Rel_Users_Year]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Rel_Users_Year];
GO
IF OBJECT_ID(N'[dbo].[Trip_Bus]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Trip_Bus];
GO
IF OBJECT_ID(N'[dbo].[Trips]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Trips];
GO
IF OBJECT_ID(N'[dbo].[TripType]', 'U') IS NOT NULL
    DROP TABLE [dbo].[TripType];
GO
IF OBJECT_ID(N'[dbo].[Users]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Users];
GO
IF OBJECT_ID(N'[dbo].[Users_AdditionalDetails]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Users_AdditionalDetails];
GO
IF OBJECT_ID(N'[dbo].[UserType]', 'U') IS NOT NULL
    DROP TABLE [dbo].[UserType];
GO
IF OBJECT_ID(N'[dbo].[NotificType]', 'U') IS NOT NULL
    DROP TABLE [dbo].[NotificType];
GO
IF OBJECT_ID(N'[dbo].[REL_Users_NotificPlayers]', 'U') IS NOT NULL
    DROP TABLE [dbo].[REL_Users_NotificPlayers];
GO
IF OBJECT_ID(N'[dbo].[Configurations]', 'U') IS NOT NULL
    DROP TABLE [dbo].[Configurations];
GO

-- --------------------------------------------------
-- Creating all tables
-- --------------------------------------------------

-- Creating table 'Activity'
CREATE TABLE [dbo].[Activity] (
    [ID_Activity] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_Year] int  NOT NULL,
    [Name] varchar(100)  NULL,
    [StartDate] datetime  NULL,
    [EndDate] datetime  NULL,
    [Note] varchar(max)  NULL,
    [Removed] bit  NULL,
    [Days] varchar(100)  NULL
);
GO

-- Creating table 'BalanceSheets'
CREATE TABLE [dbo].[BalanceSheets] (
    [ID_Balance] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_UserType] int  NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_Type] int  NOT NULL,
    [ID_Year] int  NOT NULL,
    [DataMovimento] datetime  NULL,
    [Importo] decimal(18,2)  NULL,
    [Causale] varchar(max)  NULL,
    [Removed] bit  NOT NULL
);
GO

-- Creating table 'Bar'
CREATE TABLE [dbo].[Bar] (
    [ID_Bar] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [ID_Year] int  NULL,
    [ID_User] uniqueidentifier  NULL,
    [Barcode] varchar(50)  NULL,
    [DateInsert] datetime  NULL,
    [ID_Prodotto] int  NULL,
    [ID_Ordine] int  NULL,
    [Price] decimal(18,2)  NULL
);
GO

-- Creating table 'Comuni'
CREATE TABLE [dbo].[Comuni] (
    [ID_Comune] int  NOT NULL,
    [Nome] varchar(100)  NOT NULL,
    [ID_Provincia] int  NOT NULL,
    [Cap] varchar(5)  NOT NULL
);
GO

-- Creating table 'Dinners'
CREATE TABLE [dbo].[Dinners] (
    [ID_Dinner] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Name] varchar(150)  NOT NULL,
    [Price] decimal(18,2)  NOT NULL,
    [Removed] bit  NOT NULL
);
GO

-- Creating table 'Fornitori'
CREATE TABLE [dbo].[Fornitori] (
    [ID_Fornitore] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Name] varchar(100)  NULL,
    [Address] varchar(200)  NULL,
    [Cf] varchar(50)  NULL,
    [Piva] varchar(50)  NULL,
    [Removed] bit  NULL,
    [Conto] decimal(18,2)  NULL
);
GO

-- Creating table 'Grest'
CREATE TABLE [dbo].[Grest] (
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Name] varchar(100)  NULL,
    [Address] varchar(150)  NULL,
    [Zipcode] varchar(50)  NULL,
    [ID_Provincia] int  NULL,
    [ID_Comune] int  NULL,
    [Phone] varchar(50)  NULL,
    [Fax] varchar(50)  NULL,
    [Email] varchar(50)  NULL,
    [ReferName] varchar(50)  NULL,
    [ReferPhone] varchar(50)  NULL,
    [Note] varchar(max)  NULL,
    [Enable] bit  NOT NULL,
    [CostoMensa] decimal(18,2)  NOT NULL,
    [LastUpdate] datetime  NULL,
    [UserUpdate] uniqueidentifier  NULL,
    [Removed] bit  NOT NULL,
    [NumUtenti] int  NOT NULL,
    [PivaCf] varchar(50)  NULL,
    [NoteRicevuta] varchar(max)  NULL,
    [Capacity] int  NULL,
    [Logo] varchar(150)  NULL,
    [ScaricoAutomatico] bit  NULL,
    [Codice] varchar(5)  NULL,
    [NoteIscrizione] varchar(max)  NULL,
    [IscrizioneGiteGratuita] bit  NULL,
    [InvioMailAttivazioneUtenti] bit  NULL,
    [EnableSwitchUtenti] bit  NULL,
    [Privacy] varchar(150)  NULL,
    [LicenseType] char(5)  NOT NULL,
    [PdfCustom] varchar(150)  NULL,
    [TestoPdfCustom] varchar(150)  NULL,
    [AbilitaNotificaIscrizone] bit  NULL,
    [AbilitaNotificaInOut] bit  NULL,
    [AbilitaNotificaMensa] bit  NULL,
    [AbilitaNotificaMovimenti] bit  NULL,
    [AbilitaNotificaMessaggi] bit  NULL,
    [AbilitaNotificaDocumenti] bit  NULL,
    [AbilitaNotificaUtenteDaAttivare] bit  NULL,
    [AbilitaNotifiche] bit  NOT NULL,
    [IscrAssicurazione] bit  NULL,
    [IscrLimiteBar] bit  NULL,
    [IscrAvatar] bit  NULL,
    [IscrDatiFiscali] bit  NULL,
    [IscrSettimane] bit  NULL,
    [IscrNumeroSettimane] int  NULL,
    [IscrTaglia] bit  NULL,
    [IscrFreqCosti] bit  NOT NULL,
    [IscrClasseFrequentata] bit  NOT NULL,
    [IscrElencoDelegati] bit  NOT NULL,
    [Catechismo] bit  NULL
);
GO

-- Creating table 'Grest_Files'
CREATE TABLE [dbo].[Grest_Files] (
    [ID_File] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [DataInsert] datetime  NULL,
    [FileType] varchar(50)  NULL,
    [FileName] varchar(100)  NULL,
    [FileExtension] varchar(10)  NULL,
    [Title] nvarchar(200)  NULL,
    [Text] nvarchar(500)  NULL,
    [Position] int  NULL,
    [SharedWith] nvarchar(128)  NULL,
    [DateExpiration] datetime  NULL,
    [ID_FileType] int  NOT NULL
);
GO

-- Creating table 'Grest_FilesType'
CREATE TABLE [dbo].[Grest_FilesType] (
    [ID_FileType] int IDENTITY(1,1) NOT NULL,
    [Description] nvarchar(150)  NOT NULL,
    [ExpirationMonths] int  NULL
);
GO

-- Creating table 'Grest_Iscrizione_Type'
CREATE TABLE [dbo].[Grest_Iscrizione_Type] (
    [ID_Iscrizione_Type] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Description] varchar(100)  NULL,
    [Price] decimal(18,2)  NULL,
    [Removed] bit  NOT NULL,
    [LastUpdate] datetime  NULL,
    [UserUpdate] datetime  NULL,
    [Scarico] int  NULL
);
GO

-- Creating table 'Grest_Progressivo_Ricevuta'
CREATE TABLE [dbo].[Grest_Progressivo_Ricevuta] (
    [ID_Progressivo] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_Year] int  NOT NULL,
    [Progressivo] int  NOT NULL
);
GO

-- Creating table 'Grest_UserAdditionalDetails'
CREATE TABLE [dbo].[Grest_UserAdditionalDetails] (
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Key] nvarchar(150)  NOT NULL,
    [FieldType] tinyint  NOT NULL,
    [Label] nvarchar(150)  NOT NULL,
    [Sort] int  NOT NULL
);
GO

-- Creating table 'Grest_Year'
CREATE TABLE [dbo].[Grest_Year] (
    [ID_Year] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Title] int  NOT NULL,
    [Removed] bit  NOT NULL
);
GO

-- Creating table 'GrestFatt'
CREATE TABLE [dbo].[GrestFatt] (
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_GrestFatt] uniqueidentifier  NOT NULL,
    [Name] varchar(100)  NOT NULL,
    [Address] varchar(100)  NOT NULL,
    [ZipCode] varchar(100)  NOT NULL,
    [ID_Provincia] int  NOT NULL,
    [ID_Comune] int  NOT NULL,
    [Phone] varchar(50)  NOT NULL,
    [Fax] varchar(50)  NULL,
    [Email] varchar(50)  NULL,
    [PivaCf] varchar(50)  NOT NULL
);
GO

-- Creating table 'Groups'
CREATE TABLE [dbo].[Groups] (
    [ID_Group] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_Year] int  NOT NULL,
    [Name] varchar(100)  NULL,
    [AgeFrom] int  NULL,
    [AgeTo] int  NULL,
    [Gender] varchar(50)  NULL,
    [Removed] bit  NULL,
    [Classe] varchar(50)  NOT NULL,
    [Description] nvarchar(max)  NULL,
    [BirthdayFrom] datetime  NULL,
    [BirthdayTo] datetime  NULL,
    [Trips] nvarchar(max)  NULL
);
GO

-- Creating table 'InputOutput'
CREATE TABLE [dbo].[InputOutput] (
    [ID_Input] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [ID_Year] int  NULL,
    [ID_User] uniqueidentifier  NULL,
    [DateIn] datetime  NULL,
    [DateOut] datetime  NULL,
    [Barcode] varchar(50)  NULL,
    [NoteIn] varchar(max)  NULL,
    [NoteOut] varchar(max)  NULL
);
GO

-- Creating table 'Listino'
CREATE TABLE [dbo].[Listino] (
    [ID_Listino] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [ID_Year] int  NULL,
    [ID_User] uniqueidentifier  NULL,
    [Barcode] varchar(50)  NULL,
    [DateInsert] datetime  NULL,
    [ID_Prodotto] int  NULL,
    [ID_Ordine] int  NULL,
    [Price] decimal(18,2)  NULL
);
GO

-- Creating table 'Log'
CREATE TABLE [dbo].[Log] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [userid] nvarchar(128)  NULL,
    [pagina] varchar(100)  NULL,
    [message] varchar(max)  NULL,
    [innerexception] varchar(max)  NULL,
    [stacktrace] varchar(max)  NULL,
    [source] varchar(max)  NULL,
    [DTINSERT] datetime  NULL,
    [Removed] bit  NULL,
    [DTUPDATE] datetime  NULL,
    [USERINSERT] nvarchar(128)  NULL,
    [USERUPDATE] nvarchar(128)  NULL
);
GO

-- Creating table 'Logs'
CREATE TABLE [dbo].[Logs] (
    [ID_Log] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [GrestName] varchar(100)  NULL,
    [ID_User] uniqueidentifier  NULL,
    [DataInsert] datetime  NULL,
    [Lastname] varchar(50)  NULL,
    [Name] varchar(50)  NULL,
    [Action] varchar(max)  NULL
);
GO

-- Creating table 'Mensa'
CREATE TABLE [dbo].[Mensa] (
    [ID_Mensa] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [ID_Year] int  NULL,
    [ID_User] uniqueidentifier  NULL,
    [Barcode] varchar(50)  NULL,
    [DateInsert] datetime  NULL,
    [EatWhite] bit  NULL,
    [Note] varchar(max)  NULL,
    [Mangiato] bit  NULL,
    [ID_Dinner] int  NOT NULL
);
GO

-- Creating table 'News'
CREATE TABLE [dbo].[News] (
    [ID_News] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [DatePage] datetime  NULL,
    [Title] nvarchar(500)  NULL,
    [Image] varchar(200)  NULL,
    [Intro] nvarchar(max)  NULL,
    [Text] nvarchar(max)  NULL,
    [LastUpdate] datetime  NULL,
    [UserUpdate] uniqueidentifier  NULL,
    [Enable] bit  NOT NULL,
    [Removed] bit  NOT NULL
);
GO

-- Creating table 'Pagamenti'
CREATE TABLE [dbo].[Pagamenti] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [Userid] uniqueidentifier  NULL,
    [Data] datetime  NULL,
    [Token] varchar(50)  NULL,
    [Importo] decimal(18,2)  NULL,
    [DTINSERT] datetime  NULL,
    [DTUPDATE] datetime  NULL,
    [Removed] bit  NULL,
    [USERINSERT] nvarchar(128)  NULL,
    [USERUPDATE] nvarchar(128)  NULL
);
GO

-- Creating table 'PayPalConfig'
CREATE TABLE [dbo].[PayPalConfig] (
    [ID_PaypalConfig] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [api_username] varchar(200)  NULL,
    [api_password] varchar(200)  NULL,
    [applicationid] varchar(200)  NULL,
    [api_signature] varchar(200)  NULL,
    [Removed] bit  NOT NULL,
    [street] varchar(100)  NULL,
    [cityname] varchar(100)  NULL,
    [stateorprovince] varchar(100)  NULL,
    [country] varchar(50)  NULL,
    [postalcode] int  NULL,
    [PercentualeRicarico] decimal(18,2)  NULL,
    [PayPalRicaricheAbilitate] bit  NOT NULL,
    [Valuta] varchar(3)  NULL
);
GO

-- Creating table 'Prodotti'
CREATE TABLE [dbo].[Prodotti] (
    [ID_Prodotto] int IDENTITY(1,1) NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [Name] varchar(100)  NOT NULL,
    [Price] decimal(18,2)  NOT NULL,
    [Removed] bit  NOT NULL,
    [Tipo] int  NULL
);
GO

-- Creating table 'Province'
CREATE TABLE [dbo].[Province] (
    [ID_Provincia] int  NOT NULL,
    [Nome] varchar(50)  NULL,
    [Sigla] varchar(10)  NULL,
    [ID_Regione] int  NULL
);
GO

-- Creating table 'Regioni'
CREATE TABLE [dbo].[Regioni] (
    [ID_Regione] int  NOT NULL,
    [Nome] varchar(100)  NULL
);
GO

-- Creating table 'Rel_Users'
CREATE TABLE [dbo].[Rel_Users] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_Parent] uniqueidentifier  NOT NULL,
    [ID_Child] uniqueidentifier  NOT NULL
);
GO

-- Creating table 'Rel_Users_Activity'
CREATE TABLE [dbo].[Rel_Users_Activity] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_Activity] uniqueidentifier  NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_UserType] int  NOT NULL
);
GO

-- Creating table 'Rel_Users_Groups'
CREATE TABLE [dbo].[Rel_Users_Groups] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_Group] uniqueidentifier  NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_UserType] int  NOT NULL
);
GO

-- Creating table 'Rel_Users_News'
CREATE TABLE [dbo].[Rel_Users_News] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_News] uniqueidentifier  NOT NULL,
    [Sended] bit  NOT NULL
);
GO

-- Creating table 'Rel_Users_Trip'
CREATE TABLE [dbo].[Rel_Users_Trip] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_Trip] uniqueidentifier  NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_UserType] int  NOT NULL,
    [Position] int  NULL,
    [Confirmed] bit  NOT NULL
);
GO

-- Creating table 'Rel_Users_Trip_Bus'
CREATE TABLE [dbo].[Rel_Users_Trip_Bus] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_Bus] uniqueidentifier  NULL,
    [ID_Trip] uniqueidentifier  NULL,
    [ID_User] uniqueidentifier  NULL,
    [ID_UserType] int  NULL,
    [Position] int  NULL
);
GO

-- Creating table 'Rel_Users_Year'
CREATE TABLE [dbo].[Rel_Users_Year] (
    [ID] int IDENTITY(1,1) NOT NULL,
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_Year] int  NOT NULL,
    [ID_UserType] int  NOT NULL,
    [DateInsert] datetime  NULL,
    [Enabled] bit  NOT NULL,
    [Removed] bit  NOT NULL
);
GO

-- Creating table 'Trip_Bus'
CREATE TABLE [dbo].[Trip_Bus] (
    [ID_Bus] uniqueidentifier  NOT NULL,
    [ID_Trip] uniqueidentifier  NULL,
    [Plate] varchar(50)  NULL,
    [Model] varchar(50)  NULL,
    [Note] varchar(max)  NULL,
    [MaxGuest] int  NULL,
    [Removed] bit  NULL
);
GO

-- Creating table 'Trips'
CREATE TABLE [dbo].[Trips] (
    [ID_Trip] uniqueidentifier  NOT NULL,
    [ID_Grest] uniqueidentifier  NOT NULL,
    [ID_Year] int  NULL,
    [Name] varchar(100)  NULL,
    [StartDate] datetime  NULL,
    [EndDate] datetime  NULL,
    [UnitPrice] decimal(18,2)  NULL,
    [Note] varchar(max)  NULL,
    [EntryOpen] bit  NULL,
    [Closed] bit  NULL,
    [Removed] bit  NULL,
    [Capacity] int  NULL,
    [AgeFrom] int  NULL,
    [AgeTo] int  NULL,
    [ID_TripType] int  NOT NULL,
    [PdfFile] nvarchar(150)  NULL,
    [PdfFileText] nvarchar(150)  NULL,
    [Archived] bit  NULL
);
GO

-- Creating table 'TripType'
CREATE TABLE [dbo].[TripType] (
    [ID_TripType] int IDENTITY(1,1) NOT NULL,
    [Name] varchar(50)  NOT NULL
);
GO

-- Creating table 'Users'
CREATE TABLE [dbo].[Users] (
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_UserType] int  NULL,
    [ID_Grest] uniqueidentifier  NULL,
    [ID_Year] int  NULL,
    [DateInsert] datetime  NULL,
    [Avatar] varchar(200)  NULL,
    [Lastname] varchar(50)  NULL,
    [Name] varchar(50)  NULL,
    [Email] varchar(100)  NULL,
    [Pwd] varchar(100)  NULL,
    [Barcode] varchar(50)  NULL,
    [BarcodeArchive] varchar(100)  NULL,
    [Address] varchar(100)  NULL,
    [Phone] varchar(50)  NULL,
    [Father] varchar(100)  NULL,
    [FatherPhone] varchar(50)  NULL,
    [Mother] varchar(100)  NULL,
    [MotherPhone] varchar(50)  NULL,
    [GrandparentsPhone] varchar(255)  NULL,
    [Birthday] datetime  NULL,
    [Gender] varchar(50)  NULL,
    [ID_Provincia] int  NULL,
    [ID_Comune] int  NULL,
    [MedicalInfo] varchar(max)  NULL,
    [MedicalCare] varchar(max)  NULL,
    [SchoolName] varchar(100)  NULL,
    [SchoolClass] varchar(100)  NULL,
    [SchoolCity] varchar(300)  NULL,
    [Size] varchar(50)  NULL,
    [Note] varchar(max)  NULL,
    [Enable] bit  NOT NULL,
    [Removed] bit  NOT NULL,
    [Conto] decimal(18,2)  NULL,
    [Weeks] varchar(100)  NULL,
    [ID_Parent] uniqueidentifier  NULL,
    [InsuranceN] varchar(200)  NOT NULL,
    [ParentCf] varchar(50)  NOT NULL,
    [ChildCf] varchar(50)  NOT NULL,
    [Cap] varchar(50)  NOT NULL,
    [Citta] varchar(50)  NOT NULL,
    [Provincia] varchar(50)  NOT NULL,
    [CognomeGenitoreFiscale] varchar(50)  NULL,
    [NomeGenitoreFiscale] varchar(50)  NULL,
    [IndirizzoGenitoreFiscale] varchar(50)  NULL,
    [CapGenitoreFiscale] varchar(50)  NULL,
    [CittaGenitoreFiscale] varchar(50)  NULL,
    [ProvinciaGenitoreFiscale] varchar(50)  NULL,
    [CfGenitoreFiscale] varchar(50)  NULL,
    [TempoPienoParziale] varchar(50)  NULL,
    [NoteRiservate] varchar(max)  NULL,
    [LimiteGiornalieroBar] decimal(18,2)  NULL,
    [Country] varchar(50)  NULL,
    [Extra1] varchar(200)  NULL,
    [Extra2] varchar(200)  NULL,
    [Extra3] varchar(200)  NULL,
    [DataComunione] datetime  NULL,
    [LuogoComunione] varchar(50)  NULL,
    [DataBattesimo] datetime  NULL,
    [LuogoBattesimo] varchar(50)  NULL,
    [ResponsabilitaDati] bit  NOT NULL
);
GO

-- Creating table 'Users_AdditionalDetails'
CREATE TABLE [dbo].[Users_AdditionalDetails] (
    [ID_User] uniqueidentifier  NOT NULL,
    [Key] nvarchar(150)  NOT NULL,
    [Value] nvarchar(max)  NOT NULL
);
GO

-- Creating table 'UserType'
CREATE TABLE [dbo].[UserType] (
    [ID_UserType] int IDENTITY(1,1) NOT NULL,
    [Name] varchar(50)  NULL
);
GO

-- Creating table 'NotificType'
CREATE TABLE [dbo].[NotificType] (
    [Id_NotificType] tinyint  NOT NULL,
    [Name] nvarchar(50)  NOT NULL
);
GO

-- Creating table 'REL_Users_NotificPlayers'
CREATE TABLE [dbo].[REL_Users_NotificPlayers] (
    [ID_User] uniqueidentifier  NOT NULL,
    [ID_Player] nvarchar(50)  NOT NULL,
    [ID_NotificType] tinyint  NOT NULL
);
GO

-- Creating table 'Configurations'
CREATE TABLE [dbo].[Configurations] (
    [Key] nvarchar(100)  NOT NULL,
    [Value] nvarchar(max)  NOT NULL
);
GO

-- --------------------------------------------------
-- Creating all PRIMARY KEY constraints
-- --------------------------------------------------

-- Creating primary key on [ID_Activity] in table 'Activity'
ALTER TABLE [dbo].[Activity]
ADD CONSTRAINT [PK_Activity]
    PRIMARY KEY CLUSTERED ([ID_Activity] ASC);
GO

-- Creating primary key on [ID_Balance] in table 'BalanceSheets'
ALTER TABLE [dbo].[BalanceSheets]
ADD CONSTRAINT [PK_BalanceSheets]
    PRIMARY KEY CLUSTERED ([ID_Balance] ASC);
GO

-- Creating primary key on [ID_Bar] in table 'Bar'
ALTER TABLE [dbo].[Bar]
ADD CONSTRAINT [PK_Bar]
    PRIMARY KEY CLUSTERED ([ID_Bar] ASC);
GO

-- Creating primary key on [ID_Comune] in table 'Comuni'
ALTER TABLE [dbo].[Comuni]
ADD CONSTRAINT [PK_Comuni]
    PRIMARY KEY CLUSTERED ([ID_Comune] ASC);
GO

-- Creating primary key on [ID_Dinner] in table 'Dinners'
ALTER TABLE [dbo].[Dinners]
ADD CONSTRAINT [PK_Dinners]
    PRIMARY KEY CLUSTERED ([ID_Dinner] ASC);
GO

-- Creating primary key on [ID_Fornitore] in table 'Fornitori'
ALTER TABLE [dbo].[Fornitori]
ADD CONSTRAINT [PK_Fornitori]
    PRIMARY KEY CLUSTERED ([ID_Fornitore] ASC);
GO

-- Creating primary key on [ID_Grest] in table 'Grest'
ALTER TABLE [dbo].[Grest]
ADD CONSTRAINT [PK_Grest]
    PRIMARY KEY CLUSTERED ([ID_Grest] ASC);
GO

-- Creating primary key on [ID_File] in table 'Grest_Files'
ALTER TABLE [dbo].[Grest_Files]
ADD CONSTRAINT [PK_Grest_Files]
    PRIMARY KEY CLUSTERED ([ID_File] ASC);
GO

-- Creating primary key on [ID_FileType] in table 'Grest_FilesType'
ALTER TABLE [dbo].[Grest_FilesType]
ADD CONSTRAINT [PK_Grest_FilesType]
    PRIMARY KEY CLUSTERED ([ID_FileType] ASC);
GO

-- Creating primary key on [ID_Iscrizione_Type] in table 'Grest_Iscrizione_Type'
ALTER TABLE [dbo].[Grest_Iscrizione_Type]
ADD CONSTRAINT [PK_Grest_Iscrizione_Type]
    PRIMARY KEY CLUSTERED ([ID_Iscrizione_Type] ASC);
GO

-- Creating primary key on [ID_Progressivo] in table 'Grest_Progressivo_Ricevuta'
ALTER TABLE [dbo].[Grest_Progressivo_Ricevuta]
ADD CONSTRAINT [PK_Grest_Progressivo_Ricevuta]
    PRIMARY KEY CLUSTERED ([ID_Progressivo] ASC);
GO

-- Creating primary key on [ID_Grest], [Key] in table 'Grest_UserAdditionalDetails'
ALTER TABLE [dbo].[Grest_UserAdditionalDetails]
ADD CONSTRAINT [PK_Grest_UserAdditionalDetails]
    PRIMARY KEY CLUSTERED ([ID_Grest], [Key] ASC);
GO

-- Creating primary key on [ID_Year] in table 'Grest_Year'
ALTER TABLE [dbo].[Grest_Year]
ADD CONSTRAINT [PK_Grest_Year]
    PRIMARY KEY CLUSTERED ([ID_Year] ASC);
GO

-- Creating primary key on [ID_Grest], [ID_GrestFatt] in table 'GrestFatt'
ALTER TABLE [dbo].[GrestFatt]
ADD CONSTRAINT [PK_GrestFatt]
    PRIMARY KEY CLUSTERED ([ID_Grest], [ID_GrestFatt] ASC);
GO

-- Creating primary key on [ID_Group] in table 'Groups'
ALTER TABLE [dbo].[Groups]
ADD CONSTRAINT [PK_Groups]
    PRIMARY KEY CLUSTERED ([ID_Group] ASC);
GO

-- Creating primary key on [ID_Input] in table 'InputOutput'
ALTER TABLE [dbo].[InputOutput]
ADD CONSTRAINT [PK_InputOutput]
    PRIMARY KEY CLUSTERED ([ID_Input] ASC);
GO

-- Creating primary key on [ID_Listino] in table 'Listino'
ALTER TABLE [dbo].[Listino]
ADD CONSTRAINT [PK_Listino]
    PRIMARY KEY CLUSTERED ([ID_Listino] ASC);
GO

-- Creating primary key on [ID] in table 'Log'
ALTER TABLE [dbo].[Log]
ADD CONSTRAINT [PK_Log]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID_Log] in table 'Logs'
ALTER TABLE [dbo].[Logs]
ADD CONSTRAINT [PK_Logs]
    PRIMARY KEY CLUSTERED ([ID_Log] ASC);
GO

-- Creating primary key on [ID_Mensa] in table 'Mensa'
ALTER TABLE [dbo].[Mensa]
ADD CONSTRAINT [PK_Mensa]
    PRIMARY KEY CLUSTERED ([ID_Mensa] ASC);
GO

-- Creating primary key on [ID_News] in table 'News'
ALTER TABLE [dbo].[News]
ADD CONSTRAINT [PK_News]
    PRIMARY KEY CLUSTERED ([ID_News] ASC);
GO

-- Creating primary key on [ID] in table 'Pagamenti'
ALTER TABLE [dbo].[Pagamenti]
ADD CONSTRAINT [PK_Pagamenti]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID_PaypalConfig] in table 'PayPalConfig'
ALTER TABLE [dbo].[PayPalConfig]
ADD CONSTRAINT [PK_PayPalConfig]
    PRIMARY KEY CLUSTERED ([ID_PaypalConfig] ASC);
GO

-- Creating primary key on [ID_Prodotto] in table 'Prodotti'
ALTER TABLE [dbo].[Prodotti]
ADD CONSTRAINT [PK_Prodotti]
    PRIMARY KEY CLUSTERED ([ID_Prodotto] ASC);
GO

-- Creating primary key on [ID_Provincia] in table 'Province'
ALTER TABLE [dbo].[Province]
ADD CONSTRAINT [PK_Province]
    PRIMARY KEY CLUSTERED ([ID_Provincia] ASC);
GO

-- Creating primary key on [ID_Regione] in table 'Regioni'
ALTER TABLE [dbo].[Regioni]
ADD CONSTRAINT [PK_Regioni]
    PRIMARY KEY CLUSTERED ([ID_Regione] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users'
ALTER TABLE [dbo].[Rel_Users]
ADD CONSTRAINT [PK_Rel_Users]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_Activity'
ALTER TABLE [dbo].[Rel_Users_Activity]
ADD CONSTRAINT [PK_Rel_Users_Activity]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_Groups'
ALTER TABLE [dbo].[Rel_Users_Groups]
ADD CONSTRAINT [PK_Rel_Users_Groups]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_News'
ALTER TABLE [dbo].[Rel_Users_News]
ADD CONSTRAINT [PK_Rel_Users_News]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_Trip'
ALTER TABLE [dbo].[Rel_Users_Trip]
ADD CONSTRAINT [PK_Rel_Users_Trip]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_Trip_Bus'
ALTER TABLE [dbo].[Rel_Users_Trip_Bus]
ADD CONSTRAINT [PK_Rel_Users_Trip_Bus]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID] in table 'Rel_Users_Year'
ALTER TABLE [dbo].[Rel_Users_Year]
ADD CONSTRAINT [PK_Rel_Users_Year]
    PRIMARY KEY CLUSTERED ([ID] ASC);
GO

-- Creating primary key on [ID_Bus] in table 'Trip_Bus'
ALTER TABLE [dbo].[Trip_Bus]
ADD CONSTRAINT [PK_Trip_Bus]
    PRIMARY KEY CLUSTERED ([ID_Bus] ASC);
GO

-- Creating primary key on [ID_Trip] in table 'Trips'
ALTER TABLE [dbo].[Trips]
ADD CONSTRAINT [PK_Trips]
    PRIMARY KEY CLUSTERED ([ID_Trip] ASC);
GO

-- Creating primary key on [ID_TripType] in table 'TripType'
ALTER TABLE [dbo].[TripType]
ADD CONSTRAINT [PK_TripType]
    PRIMARY KEY CLUSTERED ([ID_TripType] ASC);
GO

-- Creating primary key on [ID_User] in table 'Users'
ALTER TABLE [dbo].[Users]
ADD CONSTRAINT [PK_Users]
    PRIMARY KEY CLUSTERED ([ID_User] ASC);
GO

-- Creating primary key on [ID_User], [Key] in table 'Users_AdditionalDetails'
ALTER TABLE [dbo].[Users_AdditionalDetails]
ADD CONSTRAINT [PK_Users_AdditionalDetails]
    PRIMARY KEY CLUSTERED ([ID_User], [Key] ASC);
GO

-- Creating primary key on [ID_UserType] in table 'UserType'
ALTER TABLE [dbo].[UserType]
ADD CONSTRAINT [PK_UserType]
    PRIMARY KEY CLUSTERED ([ID_UserType] ASC);
GO

-- Creating primary key on [Id_NotificType] in table 'NotificType'
ALTER TABLE [dbo].[NotificType]
ADD CONSTRAINT [PK_NotificType]
    PRIMARY KEY CLUSTERED ([Id_NotificType] ASC);
GO

-- Creating primary key on [ID_User], [ID_Player] in table 'REL_Users_NotificPlayers'
ALTER TABLE [dbo].[REL_Users_NotificPlayers]
ADD CONSTRAINT [PK_REL_Users_NotificPlayers]
    PRIMARY KEY CLUSTERED ([ID_User], [ID_Player] ASC);
GO

-- Creating primary key on [Key] in table 'Configurations'
ALTER TABLE [dbo].[Configurations]
ADD CONSTRAINT [PK_Configurations]
    PRIMARY KEY CLUSTERED ([Key] ASC);
GO

-- --------------------------------------------------
-- Creating all FOREIGN KEY constraints
-- --------------------------------------------------

-- Creating foreign key on [ID_Grest] in table 'PayPalConfig'
ALTER TABLE [dbo].[PayPalConfig]
ADD CONSTRAINT [FK_PayPalConfig_Grest]
    FOREIGN KEY ([ID_Grest])
    REFERENCES [dbo].[Grest]
        ([ID_Grest])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_PayPalConfig_Grest'
CREATE INDEX [IX_FK_PayPalConfig_Grest]
ON [dbo].[PayPalConfig]
    ([ID_Grest]);
GO

-- Creating foreign key on [ID_Year] in table 'Rel_Users_Year'
ALTER TABLE [dbo].[Rel_Users_Year]
ADD CONSTRAINT [FK_Rel_Users_Year_Year]
    FOREIGN KEY ([ID_Year])
    REFERENCES [dbo].[Grest_Year]
        ([ID_Year])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Rel_Users_Year_Year'
CREATE INDEX [IX_FK_Rel_Users_Year_Year]
ON [dbo].[Rel_Users_Year]
    ([ID_Year]);
GO

-- Creating foreign key on [ID_News] in table 'Rel_Users_News'
ALTER TABLE [dbo].[Rel_Users_News]
ADD CONSTRAINT [FK_Rel_Users_News_News]
    FOREIGN KEY ([ID_News])
    REFERENCES [dbo].[News]
        ([ID_News])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Rel_Users_News_News'
CREATE INDEX [IX_FK_Rel_Users_News_News]
ON [dbo].[Rel_Users_News]
    ([ID_News]);
GO

-- Creating foreign key on [ID_User] in table 'Rel_Users_Year'
ALTER TABLE [dbo].[Rel_Users_Year]
ADD CONSTRAINT [FK_Rel_Users_Year_Users]
    FOREIGN KEY ([ID_User])
    REFERENCES [dbo].[Users]
        ([ID_User])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Rel_Users_Year_Users'
CREATE INDEX [IX_FK_Rel_Users_Year_Users]
ON [dbo].[Rel_Users_Year]
    ([ID_User]);
GO

-- Creating foreign key on [ID_UserType] in table 'Rel_Users_Year'
ALTER TABLE [dbo].[Rel_Users_Year]
ADD CONSTRAINT [FK_Rel_Users_Year_UserType]
    FOREIGN KEY ([ID_UserType])
    REFERENCES [dbo].[UserType]
        ([ID_UserType])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Rel_Users_Year_UserType'
CREATE INDEX [IX_FK_Rel_Users_Year_UserType]
ON [dbo].[Rel_Users_Year]
    ([ID_UserType]);
GO

-- Creating foreign key on [ID_Bar] in table 'Bar'
ALTER TABLE [dbo].[Bar]
ADD CONSTRAINT [FK_Bar_Bar]
    FOREIGN KEY ([ID_Bar])
    REFERENCES [dbo].[Bar]
        ([ID_Bar])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating foreign key on [ID_Grest] in table 'Grest_UserAdditionalDetails'
ALTER TABLE [dbo].[Grest_UserAdditionalDetails]
ADD CONSTRAINT [FK_Grest_UserAdditionalDetails_Grest]
    FOREIGN KEY ([ID_Grest])
    REFERENCES [dbo].[Grest]
        ([ID_Grest])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating foreign key on [ID_Grest] in table 'GrestFatt'
ALTER TABLE [dbo].[GrestFatt]
ADD CONSTRAINT [FK_GrestFatt_Grest]
    FOREIGN KEY ([ID_Grest])
    REFERENCES [dbo].[Grest]
        ([ID_Grest])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating foreign key on [ID_FileType] in table 'Grest_Files'
ALTER TABLE [dbo].[Grest_Files]
ADD CONSTRAINT [FK_Grest_Files_Grest_FilesType]
    FOREIGN KEY ([ID_FileType])
    REFERENCES [dbo].[Grest_FilesType]
        ([ID_FileType])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Grest_Files_Grest_FilesType'
CREATE INDEX [IX_FK_Grest_Files_Grest_FilesType]
ON [dbo].[Grest_Files]
    ([ID_FileType]);
GO

-- Creating foreign key on [ID_Listino] in table 'Listino'
ALTER TABLE [dbo].[Listino]
ADD CONSTRAINT [FK_Listino_Listino]
    FOREIGN KEY ([ID_Listino])
    REFERENCES [dbo].[Listino]
        ([ID_Listino])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating foreign key on [Userid] in table 'Pagamenti'
ALTER TABLE [dbo].[Pagamenti]
ADD CONSTRAINT [FK_Pagamenti_Users]
    FOREIGN KEY ([Userid])
    REFERENCES [dbo].[Users]
        ([ID_User])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Pagamenti_Users'
CREATE INDEX [IX_FK_Pagamenti_Users]
ON [dbo].[Pagamenti]
    ([Userid]);
GO

-- Creating foreign key on [ID_User] in table 'Rel_Users_News'
ALTER TABLE [dbo].[Rel_Users_News]
ADD CONSTRAINT [FK_Rel_Users_News_Users]
    FOREIGN KEY ([ID_User])
    REFERENCES [dbo].[Users]
        ([ID_User])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Rel_Users_News_Users'
CREATE INDEX [IX_FK_Rel_Users_News_Users]
ON [dbo].[Rel_Users_News]
    ([ID_User]);
GO

-- Creating foreign key on [ID_TripType] in table 'Trips'
ALTER TABLE [dbo].[Trips]
ADD CONSTRAINT [FK_Trips_TripType]
    FOREIGN KEY ([ID_TripType])
    REFERENCES [dbo].[TripType]
        ([ID_TripType])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_Trips_TripType'
CREATE INDEX [IX_FK_Trips_TripType]
ON [dbo].[Trips]
    ([ID_TripType]);
GO

-- Creating foreign key on [ID_User] in table 'Users_AdditionalDetails'
ALTER TABLE [dbo].[Users_AdditionalDetails]
ADD CONSTRAINT [FK_Users_AdditionalDetails_Users]
    FOREIGN KEY ([ID_User])
    REFERENCES [dbo].[Users]
        ([ID_User])
    ON DELETE CASCADE ON UPDATE NO ACTION;
GO

-- Creating foreign key on [ID_NotificType] in table 'REL_Users_NotificPlayers'
ALTER TABLE [dbo].[REL_Users_NotificPlayers]
ADD CONSTRAINT [FK_REL_Users_NotificPlayers_NotificType]
    FOREIGN KEY ([ID_NotificType])
    REFERENCES [dbo].[NotificType]
        ([Id_NotificType])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- Creating non-clustered index for FOREIGN KEY 'FK_REL_Users_NotificPlayers_NotificType'
CREATE INDEX [IX_FK_REL_Users_NotificPlayers_NotificType]
ON [dbo].[REL_Users_NotificPlayers]
    ([ID_NotificType]);
GO

-- Creating foreign key on [ID_User] in table 'REL_Users_NotificPlayers'
ALTER TABLE [dbo].[REL_Users_NotificPlayers]
ADD CONSTRAINT [FK_REL_Users_NotificPlayers_Users]
    FOREIGN KEY ([ID_User])
    REFERENCES [dbo].[Users]
        ([ID_User])
    ON DELETE NO ACTION ON UPDATE NO ACTION;
GO

-- --------------------------------------------------
-- Script has ended
-- --------------------------------------------------