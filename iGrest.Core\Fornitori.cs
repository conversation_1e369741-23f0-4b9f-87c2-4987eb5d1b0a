//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Fornitori
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Fornitori()
        {
            this.Rel_Files_Fornitori = new HashSet<Rel_Files_Fornitori>();
        }
    
        public System.Guid ID_Fornitore { get; set; }
        public System.Guid ID_Grest { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Cf { get; set; }
        public string Piva { get; set; }
        public Nullable<bool> Removed { get; set; }
        public Nullable<decimal> Conto { get; set; }
    
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Files_Fornitori> Rel_Files_Fornitori { get; set; }
    }
}
