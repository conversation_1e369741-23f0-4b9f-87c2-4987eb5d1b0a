﻿using iGrest.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace iGrest.Utils
{
	/// <summary>
	/// Interaction logic for SearchWindow.xaml
	/// </summary>
	public partial class SearchWindow : Window
	{
		public SearchWindow()
		{
			InitializeComponent();
		}

		private void btnSearch_Click(object sender, RoutedEventArgs e)
		{
			if(txtSearch.Text?.Length < 3)
			{
				MessageBox.Show("Inserisci almeno 3 lettere o numeri", "ATTENZIONE", MessageBoxButton.OK, MessageBoxImage.Warning);
				return;
			}

			using(var db = new iGrestEntities())
			{
				var grests = db.Grest
					.Where(x => x.Codice.Contains(txtSearch.Text) || x.Name.Contains(txtSearch.Text) || x.ID_Grest.ToString() == txtSearch.Text)
					.OrderBy(x => x.Name)
					.ToList();

				var result = new List<string>();

				foreach (var grest in grests) 
				{
					result.Add(grestResultTemplate(grest));
				}

				txbResult.Text = string.Join("\r\n#####################################################\r\n\r\n", result);
			}
		}

		private string grestResultTemplate(Grest grest)
		{
			var sb = new StringBuilder();

			sb.AppendLine("ID=" + grest.ID_Grest);
			sb.AppendLine("Code=" + grest.Codice);
			sb.AppendLine("Name=" + grest.Name);

			return sb.ToString();
		}

		private void txtSearch_KeyUp(object sender, KeyEventArgs e)
		{
			if(e.Key == Key.Enter)
			{
				btnSearch_Click(sender, e);
			}
		}
	}
}
