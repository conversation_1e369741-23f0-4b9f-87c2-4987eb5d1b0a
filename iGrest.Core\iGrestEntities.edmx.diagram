<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="5638f7976259405e98f95df6e6d5b7fc" Name="Diagram1">
        <EntityTypeShape EntityType="iGrest.Core.Activity" Width="1.5" PointX="12" PointY="79.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.BalanceIncomeStatements" Width="1.5" PointX="9.75" PointY="63" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.BalanceReceiptDetails" Width="1.5" PointX="23.25" PointY="78" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.BalanceSheets" Width="1.5" PointX="15" PointY="79.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Bar" Width="1.5" PointX="15" PointY="75.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Comuni" Width="1.5" PointX="5.25" PointY="79.5" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Dinners" Width="1.5" PointX="9.75" PointY="71" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.FE_CondizioniPagamento" Width="1.5" PointX="21" PointY="68.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.FE_FormatoTrasmissione" Width="1.5" PointX="21" PointY="65.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.FE_ModalitaPagamento" Width="1.5" PointX="21" PointY="61.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.FE_RegimeFiscale" Width="1.5" PointX="15.75" PointY="97.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.FE_TipoDocumento" Width="1.5" PointX="21" PointY="98.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Fornitori" Width="1.5" PointX="9.75" PointY="74.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest" Width="1.5" PointX="7.5" PointY="69.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_Files" Width="1.5" PointX="26.25" PointY="78.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_Files_Users" Width="1.5" PointX="31.5" PointY="81.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_FilesType" Width="1.5" PointX="24" PointY="68.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_Iscrizione_Type" Width="1.5" PointX="9.75" PointY="78.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_Progressivo_Ricevuta" Width="1.5" PointX="18" PointY="88.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_UserAdditionalDetails" Width="1.5" PointX="9.75" PointY="85" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Grest_Year" Width="1.5" PointX="9.75" PointY="87.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.GrestFatt" Width="1.5" PointX="18" PointY="92.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.GrestHeadquarters" Width="1.5" PointX="9.75" PointY="100.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Groups" Width="1.5" PointX="12" PointY="73.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.InputOutput" Width="1.5" PointX="18" PointY="82.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Listino" Width="1.5" PointX="18" PointY="77.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Log" Width="1.5" PointX="0.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Logs" Width="1.5" PointX="29.25" PointY="84.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Mensa" Width="1.5" PointX="15" PointY="88.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.News" Width="1.5" PointX="9.75" PointY="105.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.News_Read" Width="1.5" PointX="23.25" PointY="91.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.NotificType" Width="1.5" PointX="21" PointY="59.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Order" Width="1.5" PointX="18" PointY="72.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.OrderItem" Width="1.5" PointX="23.25" PointY="71.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Pagamenti" Width="1.5" PointX="23.25" PointY="84.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.PaymentType" Width="1.5" PointX="12.75" PointY="66.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.PayPalConfig" Width="1.5" PointX="9.75" PointY="109.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Portale" Width="1.5" PointX="5.25" PointY="73.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Prodotti" Width="1.5" PointX="14.75" PointY="69.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.ProdottiTipo" Width="1.5" PointX="12.5" PointY="70.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Province" Width="1.5" PointX="3" PointY="79.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Regioni" Width="1.5" PointX="0.75" PointY="79.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users" Width="1.5" PointX="23.25" PointY="88.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Activity" Width="1.5" PointX="26.25" PointY="74.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Groups" Width="1.5" PointX="29.25" PointY="76.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Headquarter" Width="1.5" PointX="29.25" PointY="89.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_News" Width="1.5" PointX="23.25" PointY="94" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.REL_Users_NotificPlayers" Width="1.5" PointX="23.25" PointY="75.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Trip" Width="1.5" PointX="26.25" PointY="85" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Trip_Bus" Width="1.5" PointX="31.75" PointY="85.125" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_TripsPrice" Width="1.5" PointX="29.25" PointY="80.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Rel_Users_Year" Width="1.5" PointX="15" PointY="93.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.SatispayConfig" Width="1.5" PointX="9.75" PointY="119.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.SatispayTransaction" Width="1.5" PointX="2.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.SchoolClass" Width="1.5" PointX="18.75" PointY="65.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.StripeConfig" Width="1.5" PointX="9.75" PointY="123.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.StripeTransaction" Width="1.5" PointX="2.75" PointY="3.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.TransactionType" Width="1.5" PointX="12.75" PointY="94.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Triage" Width="1.5" PointX="17.75" PointY="68.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Triage_Categorie" Width="1.5" PointX="15.5" PointY="66.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Triage_InputOutput" Width="1.5" PointX="26.25" PointY="71.625" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Trip_Bus" Width="1.5" PointX="28.5" PointY="97.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Trips" Width="1.5" PointX="26.25" PointY="90.5" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.TripsMaster" Width="1.5" PointX="12" PointY="83.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.TripsPrice" Width="1.5" PointX="20.25" PointY="94.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.TripType" Width="1.5" PointX="9.75" PointY="81.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Users" Width="1.5" PointX="21" PointY="71.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.Users_AdditionalDetails" Width="1.5" PointX="23.25" PointY="99.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.UserType" Width="1.5" PointX="18" PointY="100.5" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrest.Core.W_BalanceUsers" Width="1.5" PointX="4.75" PointY="0.75" IsExpanded="true" />
        <AssociationConnector Association="iGrest.Core.FK_Activity_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Activity_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Activity_Activity" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceIncomeStatements_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_BalanceIncomeStatements" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_BalanceIncomeStatements" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_BalanceSheets" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_FE_CondizioniPagamento" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_FE_FormatoTrasmissione" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_FE_ModalitaPagamento" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_FE_TipoDocumento" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceReceiptDetails_Grest_Files" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_PaymentType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_TransactionType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_BalanceSheets_Users1" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_BalanceSheets" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_BalanceSheets" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_BalanceSheets_Deposit" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_TripsPrice_BalanceSheets" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Bar_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Bar_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Bar_Order" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Bar_Prodotti" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Bar_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Comuni_Province" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Comuni" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_Comuni" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Dinners_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Mensa_Dinners" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_GrestFatt_FE_RegimeFiscale" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Fornitori_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Iscrizione_Type_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Portale" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Progressivo_Ricevuta_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Province" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_UserAdditionalDetails_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Year_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_GrestFatt_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_GrestHeadquarters_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Groups_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_InputOutput_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Listino_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Logs_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Mensa_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_News_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Order_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_PayPalConfig_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Prodotti_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_SatispayConfig_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_StripeConfig_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Triage_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trips_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Grest_FilesType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Users_Grest_Files" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Users1" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Files_Users_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Progressivo_Ricevuta_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Grest_Progressivo_Ricevuta_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Groups_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_InputOutput_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Listino_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Mensa_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Order_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Year_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trips_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_Grest_Year" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_GrestFatt_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_InputOutput_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Mensa_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Headquarter_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trips_TripType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_GrestHeadquarters" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Groups_Groups" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_InputOutput_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_InputOutput_UsersCreated" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Triage_InputOutput_InputOutput" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Listino_Listino" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Listino_Prodotti" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Listino_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Logs_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Mensa_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_News_Read_News" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_News_News" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_News_Read_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_REL_Users_NotificPlayers_NotificType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Order_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_OrderItem_Order" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_OrderItem_Prodotti" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Pagamenti_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Prodotti_ProdottiTipo" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Province_Regioni" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_Province" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Users_Parent" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Activity_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Activity_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Groups_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Groups_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Headquarter_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_News_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_REL_Users_NotificPlayers_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Trips" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Bus_Trip_Bus" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Bus_Trips" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Bus_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Trip_Bus_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_TripsPrice_Trips" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_TripsPrice_TripsPrice" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_TripsPrice_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Year_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Rel_Users_Year_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_SchoolClass" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Triage_InputOutput_Triage" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Triage_Triage_Categorie" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trip_Bus_Trips" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trips_TripsMaster" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Trips_TripType_1" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_TripsMaster" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsMaster_TripType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsPrice_TripsMaster" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_TripsPrice_UserType" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_AdditionalDetails_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.FK_Users_Users" ManuallyRouted="false" />
        <AssociationConnector Association="iGrest.Core.Rel_Trips_Groups" ManuallyRouted="false" />
      </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>