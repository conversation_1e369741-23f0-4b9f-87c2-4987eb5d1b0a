﻿using iGrest.Core;
using iGrest.Jobs.Loggers;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Threading;
using System.Web;

namespace iGrest.Jobs.Commands
{
   public class AlignSubscriptionAttachmentCommand : ICommand
   {
      private readonly ILogger _logger;

      private readonly DBLogger _dbLogger;

      private IDictionary<string, Action> _actions = new Dictionary<string, Action>();

      private string[] _args = null;

      private static string PrivacyServerPath
      {
         get
         {
            string path = ConfigurationManager.AppSettings["privacyPath"];
            return path + "\\";
         }
      }

      private static string PdfCustomServerPath
      {
         get
         {
            string path = ConfigurationManager.AppSettings["pdfCustomPath"];
            return path + "\\";
         }
      }

      private static string PdfCustom_2ServerPath
      {
         get
         {
            string path = ConfigurationManager.AppSettings["pdfCustom2Path"];
            return path + "\\";
         }
      }

      public AlignSubscriptionAttachmentCommand(LoggerFactory loggerFactory)
      {
         _logger = loggerFactory.CreateLogger<AlignSubscriptionAttachmentCommand>();

         _dbLogger = new DBLogger(loggerFactory);

         init();
      }

      private void init()
      {
         _actions.Add(AlignSubscriptionAction.Align, _align);
      }

      private void _align()
      {
         using (var db = new iGrestEntities())
         {
            try
            {
               var gs = db.Grest
                  .Where(x => !x.Removed && x.Privacy != null || x.PdfCustom != null || x.PdfCustom_2 != null)
                  .ToList();

               var attachments = db.SubscriptionAttachments.ToList();

               foreach (var g in gs)
               {
                  if (!string.IsNullOrWhiteSpace(g.Privacy))
                  {
                     if (!attachments.Any(x => x.Title == g.TestoPrivacy && x.ID_Grest == g.ID_Grest))
                     {
                        db.SubscriptionAttachments.Add(newAttachment(g, g.Privacy, g.TestoPrivacy));
                     }
                  }

                  if (!string.IsNullOrWhiteSpace(g.PdfCustom))
                  {
                     if (!attachments.Any(x => x.Title == g.TestoPdfCustom && x.ID_Grest == g.ID_Grest))
                     {
                        var newFile = copyFile(g.PdfCustom, PdfCustomServerPath, PrivacyServerPath);

                        db.SubscriptionAttachments.Add(newAttachment(g, newFile, g.TestoPdfCustom));
                     }
                  }

                  if (!string.IsNullOrWhiteSpace(g.PdfCustom_2))
                  {
                     if (!attachments.Any(x => x.Title == g.TestoPdfCustom_2 && x.ID_Grest == g.ID_Grest))
                     {
                        var newFile = copyFile(g.PdfCustom_2, PdfCustom_2ServerPath, PrivacyServerPath);

                        db.SubscriptionAttachments.Add(newAttachment(g, newFile, g.TestoPdfCustom_2));
                     }
                  }
               }

               db.SaveChanges();

               _logger.LogInformation("Subscription attachment saved successfully!");
            }
            catch(Exception ex)
            {
               _logger.LogError($"Align subscription attachment error - ex={ex}");
            }
         }
      }

      private string copyFile(string file, string currentPath, string newPath)
      {
         var now = DateTime.Now;
         var prefix = now.ToString("yyyyMMddHHmmssfff");
         var newFileName = $"{prefix}_{file}";
         var oldF = Path.Combine(currentPath, file);
         var newF = Path.Combine(newPath, newFileName);

         Thread.Sleep(100);

         if(File.Exists(oldF))
            File.Copy(oldF, newF);

         return newFileName;
      }

      private SubscriptionAttachments newAttachment(Grest g, string filename, string title)
      {
         return new SubscriptionAttachments
         {
            ID_Grest = g.ID_Grest,
            IsMandatory = true,
            Enabled = true,
            CreatedDate = DateTime.Now,
            FileName = filename,
            Title = title
         };
      }

      public void Execute(string[] args)
      {
         _dbLogger.Log($"Execute command - args={string.Join(",", args)}");

         try
         {
            _args = args;

            _actions[_args[1]].Invoke();
         }
         catch (Exception ex)
         {
            _dbLogger.Log($"Error execute command - args={string.Join(",", _args)} | ex={ex}");
         }
      }
   }

   public class AlignSubscriptionAction
   {
      public const string Align = "align";
   }
}
