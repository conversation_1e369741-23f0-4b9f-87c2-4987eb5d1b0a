//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Rel_Users_Year
    {
        public int ID { get; set; }
        public System.Guid ID_User { get; set; }
        public int ID_Year { get; set; }
        public int ID_UserType { get; set; }
        public Nullable<System.DateTime> DateInsert { get; set; }
        public bool Enabled { get; set; }
        public bool Removed { get; set; }
        public bool Qualified { get; set; }
        public Nullable<System.DateTime> DateExpiration { get; set; }
    
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual Users Users { get; set; }
        public virtual UserType UserType { get; set; }
    }
}
