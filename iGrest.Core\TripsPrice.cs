//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TripsPrice
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public TripsPrice()
        {
            this.Rel_Users_TripsPrice = new HashSet<Rel_Users_TripsPrice>();
        }
    
        public long ID_TripPrice { get; set; }
        public System.Guid ID_TripMaster { get; set; }
        public decimal Price { get; set; }
        public Nullable<System.DateTime> PaymentDate { get; set; }
        public int ID_UserType { get; set; }
        public string Description { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        public virtual UserType UserType { get; set; }
        public virtual TripsMaster TripsMaster { get; set; }
    }
}
