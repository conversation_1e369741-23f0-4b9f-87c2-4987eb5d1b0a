﻿using iGrest.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace iGrest.Jobs.Services
{
	public class PortalService
	{
		public Portale GetPortal(string name)
		{
			using (var db = new iGrestEntities())
			{
				var o = db.Portale
				.AsQueryable();

				var result = o.SingleOrDefault(x => x.Nome == name);
				if (result == null)
					return null;

				return result;
			}
		}

		public Portale GetPortalByGrest(Guid id_grest)
		{
			using (var db = new iGrestEntities())
			{
				var o = db.Portale
				.AsQueryable();

				var result = o.FirstOrDefault(x => x.Grest.Any(y => y.ID_Grest == id_grest));
				if (result == null)
					return o.FirstOrDefault(x => x.Nome == "Squby");

				return result;
			}
		}
	}
}
