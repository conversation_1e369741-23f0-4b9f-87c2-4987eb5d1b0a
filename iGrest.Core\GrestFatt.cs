//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class GrestFatt
    {
        public System.Guid ID_Grest { get; set; }
        public System.Guid ID_GrestFatt { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string ZipCode { get; set; }
        public int ID_Provincia { get; set; }
        public int ID_Comune { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string PivaCf { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public string Firma { get; set; }
        public string NoteRicevuta { get; set; }
        public string ID_RegimeFiscale { get; set; }
        public string REA_Ufficio { get; set; }
        public string REA_Numero { get; set; }
        public string REA_CapitaleSociale { get; set; }
        public string REA_SocioUnico { get; set; }
        public string REA_StatoLiquidazione { get; set; }
        public string IBAN { get; set; }
        public string Trasmittente_Codice { get; set; }
        public string Trasmittente_Country { get; set; }
        public decimal Iva { get; set; }
        public string Sezionale { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        public virtual FE_RegimeFiscale FE_RegimeFiscale { get; set; }
    }
}
