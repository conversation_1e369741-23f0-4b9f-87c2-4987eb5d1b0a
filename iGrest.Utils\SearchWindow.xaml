﻿<Window x:Class="iGrest.Utils.SearchWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:iGrest.Utils"
        mc:Ignorable="d"
        Title="SearchWindow" Height="450" Width="800" Padding="20">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <Grid Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Label>Cod./Name</Label>
            <TextBox Grid.Column="1" x:Name="txtSearch" KeyUp="txtSearch_KeyUp"></TextBox>
            <Button Grid.Column="2" Click="btnSearch_Click" x:Name="btnSearch">Cerca</Button>
        </Grid>

        <ScrollViewer Margin="10" Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
            <TextBox Margin="10" x:Name="txbResult" 
                Background="Transparent"
                BorderThickness="0"
                IsReadOnly="True"
                TextWrapping="Wrap" />
        </ScrollViewer>
    </Grid>
</Window>
