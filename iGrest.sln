﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.1.32328.378
MinimumVisualStudioVersion = 10.0.40219.1
Project("{E24C65DC-7377-472B-9ABA-BC803B73C61A}") = "iGrest.WebSite", "iGrest.WebSite\", "{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}"
	ProjectSection(WebsiteProperties) = preProject
		TargetFrameworkMoniker = ".NETFramework,Version%3Dv4.8"
		ProjectReferences = "{8b37f093-13ac-405e-82b4-43ccd97a207e}|iGrest.Core.dll;"
		Debug.AspNetCompiler.VirtualPath = "/localhost_52833"
		Debug.AspNetCompiler.PhysicalPath = "iGrest.WebSite\"
		Debug.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52833\"
		Debug.AspNetCompiler.Updateable = "true"
		Debug.AspNetCompiler.ForceOverwrite = "true"
		Debug.AspNetCompiler.FixedNames = "false"
		Debug.AspNetCompiler.Debug = "True"
		Release.AspNetCompiler.VirtualPath = "/localhost_52833"
		Release.AspNetCompiler.PhysicalPath = "iGrest.WebSite\"
		Release.AspNetCompiler.TargetPath = "PrecompiledWeb\localhost_52833\"
		Release.AspNetCompiler.Updateable = "true"
		Release.AspNetCompiler.ForceOverwrite = "true"
		Release.AspNetCompiler.FixedNames = "false"
		Release.AspNetCompiler.Debug = "False"
		VWDPort = "52833"
		SlnRelativePath = "iGrest.WebSite\"
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iGrest.Core", "iGrest.Core\iGrest.Core.csproj", "{8B37F093-13AC-405E-82B4-43CCD97A207E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iGrest.Jobs", "iGrest.Jobs\iGrest.Jobs.csproj", "{4209A126-C4D2-4963-ADD3-38D086BF6C9C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iGrest.Utils", "iGrest.Utils\iGrest.Utils.csproj", "{956E8D21-6220-40F1-853A-3E3BD9AF74EE}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|iPhone = Debug|iPhone
		Debug|iPhoneSimulator = Debug|iPhoneSimulator
		Release|Any CPU = Release|Any CPU
		Release|iPhone = Release|iPhone
		Release|iPhoneSimulator = Release|iPhoneSimulator
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|iPhone.Build.0 = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|Any CPU.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|Any CPU.Build.0 = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|iPhone.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|iPhone.Build.0 = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{C06D15D0-F8DB-4AC7-AAB2-EC809EF3AC04}.Release|iPhoneSimulator.Build.0 = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|iPhone.Build.0 = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|iPhone.ActiveCfg = Release|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|iPhone.Build.0 = Release|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{8B37F093-13AC-405E-82B4-43CCD97A207E}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|iPhone.Build.0 = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|iPhone.ActiveCfg = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|iPhone.Build.0 = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{4209A126-C4D2-4963-ADD3-38D086BF6C9C}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|iPhone.ActiveCfg = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|iPhone.Build.0 = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|iPhoneSimulator.ActiveCfg = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Debug|iPhoneSimulator.Build.0 = Debug|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|Any CPU.Build.0 = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|iPhone.ActiveCfg = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|iPhone.Build.0 = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|iPhoneSimulator.ActiveCfg = Release|Any CPU
		{956E8D21-6220-40F1-853A-3E3BD9AF74EE}.Release|iPhoneSimulator.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {0DA5CE9D-7434-4148-B897-7A06B1A9D8A9}
	EndGlobalSection
EndGlobal
