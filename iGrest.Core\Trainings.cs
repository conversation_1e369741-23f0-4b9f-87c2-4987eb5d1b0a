//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Trainings
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Trainings()
        {
            this.Rel_Users_Training = new HashSet<Rel_Users_Training>();
            this.Children = new HashSet<Trainings>();
            this.Groups = new HashSet<Groups>();
        }
    
        public System.Guid ID_Training { get; set; }
        public string Name { get; set; }
        public System.DateTime StartDate { get; set; }
        public System.DateTime EndDate { get; set; }
        public string Note { get; set; }
        public bool EntryOpen { get; set; }
        public bool Closed { get; set; }
        public bool Archived { get; set; }
        public bool Removed { get; set; }
        public Nullable<int> Capacity { get; set; }
        public string AttachmentFile { get; set; }
        public string AttachmentText { get; set; }
        public bool Recurring { get; set; }
        public Nullable<System.DateTime> OpenRegStartDate { get; set; }
        public Nullable<System.DateTime> OpenRegEndDate { get; set; }
        public Nullable<int> CloseRegBeforeHours { get; set; }
        public bool UnsubscribeEnabled { get; set; }
        public bool RecurringAdvanced { get; set; }
        public bool Enabled { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_Year { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public Nullable<System.Guid> ID_Parent { get; set; }
        public Nullable<System.DateTime> LastNotificSendedDate { get; set; }
        public Nullable<int> RepeatEvery { get; set; }
        public Nullable<byte> RepeatEveryType { get; set; }
        public string HoursStr { get; set; }
        public string CssClass { get; set; }
        public string Location { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Training> Rel_Users_Training { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trainings> Children { get; set; }
        public virtual Trainings Parent { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Groups> Groups { get; set; }
    }
}
