//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Trip_Bus
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Trip_Bus()
        {
            this.Rel_Users_Trip_Bus = new HashSet<Rel_Users_Trip_Bus>();
        }
    
        public System.Guid ID_Bus { get; set; }
        public Nullable<System.Guid> ID_Trip { get; set; }
        public string Plate { get; set; }
        public string Model { get; set; }
        public string Note { get; set; }
        public Nullable<int> MaxGuest { get; set; }
        public Nullable<bool> Removed { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Trip_Bus> Rel_Users_Trip_Bus { get; set; }
        public virtual Trips Trips { get; set; }
    }
}
