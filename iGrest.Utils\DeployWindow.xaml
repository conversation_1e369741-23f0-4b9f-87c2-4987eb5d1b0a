﻿<Window x:Class="iGrest.Utils.DeployWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:iGrest.Utils"
        mc:Ignorable="d"
        Title="DeployWindow" Height="450" Width="800" Padding="20">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        
        <StackPanel Margin="10" HorizontalAlignment="Center" Orientation="Horizontal">
            <RadioButton Margin="10" x:Name="rbStaging" GroupName="Environment" IsChecked="True">STAGING</RadioButton>
            <RadioButton Margin="10" x:Name="rbProd" GroupName="Environment">PROD</RadioButton>
        </StackPanel>

        <CheckBox Margin="10" Grid.Row="1" x:Name="chkChangeDB" IsChecked="False">Database is changed?</CheckBox>

        <Grid Grid.Row="2" Margin="10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>

            <Label>Additional files (splitted by ",")</Label>
            <TextBox x:Name="txtAdditionalFiles" Grid.Column="1" TextWrapping="Wrap"></TextBox>
        </Grid>

        <Button Margin="10" Grid.Row="3" FontSize="20" Padding="20" x:Name="btnStart" Click="btnStart_Click">
            START
        </Button>

        <Button Margin="10" Visibility="Collapsed" Grid.Row="3" FontSize="20" Padding="20" x:Name="btnStop" Click="btnStop_Click">
            STOP
        </Button>

        <ScrollViewer Margin="10" Grid.Row="4" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" x:Name="sv">
            <TextBox Margin="10" x:Name="txbResult" 
                Background="Transparent"
                BorderThickness="0"
                IsReadOnly="True"
                TextChanged="txbResult_TextChanged"
                TextWrapping="Wrap" />
        </ScrollViewer>
    </Grid>
</Window>
