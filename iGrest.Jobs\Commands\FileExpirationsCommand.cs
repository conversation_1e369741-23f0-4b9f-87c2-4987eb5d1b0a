﻿using iGrest.Core;
using iGrest.Jobs.Loggers;
using iGrest.Jobs.Services;
using Microsoft.Extensions.Logging;
using Serilog.Core;
using System;
using System.Collections.Generic;
using System.Data.Entity;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iGrest.Jobs.Commands
{
	public class FileExpirationsCommand : ICommand
	{
		private readonly ILogger _logger;

		private readonly DBLogger _dbLogger;

		private readonly PortalService _portalService;

		private IDictionary<string, Action> _actions = new Dictionary<string, Action>();

		private string[] _args = null;

		public FileExpirationsCommand(LoggerFactory loggerFactory) 
		{
			_logger = loggerFactory.CreateLogger<FileExpirationsCommand>();

			_dbLogger = new DBLogger(loggerFactory);

			_portalService = new PortalService();

			init();
		}

		public void Execute(string[] args)
		{
			_dbLogger.Log($"Execute command - args={string.Join(",", args)}");

			try
			{
				_args = args;

				_actions[_args[1]].Invoke();
			}
			catch (Exception ex)
			{
				_dbLogger.Log($"Error execute command - args={string.Join(",", _args)} | ex={ex}");
			}
		}

		private void init()
		{
			_actions.Add(FileExpirationsAction.SendNotific, _sendNotific);
		}

		private void _sendNotific()
		{
			using(var db = new iGrestEntities())
			{
				var today = DateTime.Now.Date;

				var idGrest = default(Guid?);

				var days = new DateTime[] {
					today,
					today.AddDays(7),
					today.AddDays(30)
				};

				if (!_args[2].Equals("default", StringComparison.InvariantCultureIgnoreCase))
				{
					days = _args[2].Split(',')
						.Select(x => today.AddDays(int.Parse(x))).ToArray();
				}

				var maxDay = days.Max();

				if(_args.Length > 3)
				{
					idGrest = Guid.Parse(_args[3]);
				}

				var q = db.Grest_Files
					.AsQueryable();

				if(idGrest.HasValue)
					q = q.Where(x => x.ID_Grest == idGrest.Value);
				else
				{
					q = q.Where(x => x.Grest.AbilitaNotifiche);
				}

				q = q
					.Where(x => x.DateExpiration.HasValue && DbFunctions.TruncateTime(x.DateExpiration.Value) >= DbFunctions.TruncateTime(today) 
						&& DbFunctions.TruncateTime(x.DateExpiration.Value) <= DbFunctions.TruncateTime(maxDay));

				var files = q.ToList();

				var predicates = new List<Func<DateTime?, bool>>();

				foreach (var day in days)
				{
					predicates.Add(dt => dt.HasValue && dt.Value.Date == day.Date);
				}

				files = files.Where(x => predicates.Any(p => p(x.DateExpiration)))
					.ToList();

				foreach (var file in files) 
				{
					var id_grest = file.ID_Grest;

					_sendNotific(new[] { file.Users1 }, file.ID_Grest, file);
				}
			}
		}

		private void _sendNotific(Users[] users, Guid id_grest, Grest_Files file)
		{
			try
			{
				List<string> player_ids = new List<string>();

				//notifica messaggi al genitore e all'utente se flag Messaggi true e notifica abilitata				
				foreach (var u in users)
				{
					player_ids.AddRange(new OneSignalSender().GetAllAndParentPlayers(u.ID_User.ToString()));
				}

				#region invio notifica
				if (player_ids != null
					&& player_ids.Count > 0)
				{
					var intro = $"Documento in scadenza o scaduto: {file.Title}";

					if (intro.Length > 80)
					{
						intro = intro.Substring(0, 77) + "...";
					}

					var message = intro;

					if (string.IsNullOrEmpty(message))
						message = file.Title;

					var contents = new
					{
						en = message,
						it = message
					};

					var portale = _portalService.GetPortalByGrest(id_grest);

					var url = "admin/user_files.aspx";

					var buttons = new[] {
						new {
							id = "read-more-button",
							text = "Vai sul sito",
							icon = "http://i.imgur.com/MIxJp1L.png",
							url = url
						}
					};

					new OneSignalSender().SendNotific(portale, player_ids.ToArray(), contents, buttons, url);
				}
				#endregion
			}
			catch(Exception ex)
			{
				_logger.LogError($"Send notific error - ex={ex}");
			}
		}

		public class FileExpirationsAction
		{
			public const string SendNotific = "send-notific";
		}
	}
}
