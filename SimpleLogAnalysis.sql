-- =====================================================
-- SCRIPT DI ANALISI SEMPLICE TABELLE LOG
-- Data: 07/06/2025
-- Scopo: Capire la struttura delle tabelle di log senza assumere nomi colonne
-- =====================================================

USE iGrest;
GO

PRINT '=== ANALISI SEMPLICE TABELLE LOG ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. STRUTTURA TABELLA LOGS
-- =====================================================

PRINT '1. STRUTTURA TABELLA LOGS'
PRINT '-------------------------'

SELECT 
    COLUMN_NAME as Colonna,
    DATA_TYPE as Tipo,
    IS_NULLABLE as Nullable,
    ISNULL(CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR), 'N/A') as LunghezzaMax
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Logs'
ORDER BY ORDINAL_POSITION;

PRINT ''

-- =====================================================
-- 2. STRUTTURA TABELLA LOG
-- =====================================================

PRINT '2. STRUTTURA TABELLA LOG'
PRINT '------------------------'

SELECT 
    COLUMN_NAME as Colonna,
    DATA_TYPE as Tipo,
    IS_NULLABLE as Nullable,
    ISNULL(CAST(CHARACTER_MAXIMUM_LENGTH AS VARCHAR), 'N/A') as LunghezzaMax
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Log'
ORDER BY ORDINAL_POSITION;

PRINT ''

-- =====================================================
-- 3. CONTEGGIO RECORD
-- =====================================================

PRINT '3. CONTEGGIO RECORD'
PRINT '-------------------'

SELECT 'Logs' as Tabella, COUNT(*) as NumeroRecord FROM Logs
UNION ALL
SELECT 'Log' as Tabella, COUNT(*) as NumeroRecord FROM Log;

PRINT ''

-- =====================================================
-- 4. ANALISI FILE DI LOG SQL SERVER
-- =====================================================

PRINT '4. ANALISI FILE DI LOG SQL SERVER'
PRINT '----------------------------------'

SELECT 
    name as NomeFile,
    type_desc as TipoFile,
    physical_name as PercorsoFisico,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as SpazioUsatoMB,
    CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024 AS DECIMAL(15,2)) as SpazioLiberoMB
FROM sys.database_files;

PRINT ''

-- =====================================================
-- 5. MODALITÀ RECOVERY DATABASE
-- =====================================================

PRINT '5. MODALITÀ RECOVERY DATABASE'
PRINT '------------------------------'

SELECT 
    name as NomeDatabase,
    recovery_model_desc as ModelloRecovery,
    log_reuse_wait_desc as MotivoAttesaRiutilizzoLog
FROM sys.databases 
WHERE name = 'iGrest';

PRINT ''

-- =====================================================
-- 6. INFORMAZIONI BACKUP
-- =====================================================

PRINT '6. INFORMAZIONI BACKUP'
PRINT '----------------------'

SELECT 
    database_name,
    type,
    backup_start_date,
    backup_finish_date,
    DATEDIFF(day, backup_start_date, GETDATE()) as GiorniDaBackup
FROM msdb.dbo.backupset 
WHERE database_name = 'iGrest'
ORDER BY backup_start_date DESC;

PRINT ''
PRINT '=== ANALISI SEMPLICE COMPLETATA ==='