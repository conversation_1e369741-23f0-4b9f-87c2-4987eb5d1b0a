//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Rel_UsersType
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Rel_UsersType()
        {
            this.Rel_Users = new HashSet<Rel_Users>();
            this.Rel_UsersType1 = new HashSet<Rel_UsersType>();
            this.TripsDiscountConditions = new HashSet<TripsDiscountConditions>();
        }
    
        public int ID_Rel_UsersType { get; set; }
        public string Name { get; set; }
        public bool EnableAnagrafica { get; set; }
        public Nullable<int> ID_Linked_Rel_UsersType { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users> Rel_Users { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_UsersType> Rel_UsersType1 { get; set; }
        public virtual Rel_UsersType Rel_UsersType2 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsDiscountConditions> TripsDiscountConditions { get; set; }
    }
}
