//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Grest_Document
    {
        public System.Guid ID_Document { get; set; }
        public string Name { get; set; }
        public System.DateTime CreatedDate { get; set; }
        public bool Enabled { get; set; }
        public bool Removed { get; set; }
        public bool Archived { get; set; }
        public Nullable<System.DateTime> RemovedDate { get; set; }
        public Nullable<System.DateTime> ArchivedDate { get; set; }
        public System.DateTime LastUpdatedDate { get; set; }
        public System.Guid LastUpdatedUserId { get; set; }
        public System.Guid ID_Grest { get; set; }
        public string Filename { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public byte Status { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Users Users { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
    }
}
