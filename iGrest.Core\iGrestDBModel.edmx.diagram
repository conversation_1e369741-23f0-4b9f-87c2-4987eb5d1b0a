<?xml version="1.0" encoding="utf-8"?>
<edmx:Edmx Version="3.0" xmlns:edmx="http://schemas.microsoft.com/ado/2009/11/edmx">
 <!-- EF Designer content (DO NOT EDIT MANUALLY BELOW HERE) -->
  <edmx:Designer xmlns="http://schemas.microsoft.com/ado/2009/11/edmx">
    <!-- Diagram content (shape and connector positions) -->
    <edmx:Diagrams>
      <Diagram DiagramId="93b6a6989a6f4b6dbab7c4f5f04a52ee" Name="Diagram1" ZoomLevel="65">
        <EntityTypeShape EntityType="iGrestModel.Activity" Width="1.5" PointX="4.75" PointY="8.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Bar" Width="1.5" PointX="9.75" PointY="7.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Comuni" Width="1.5" PointX="11.75" PointY="7.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Dinners" Width="1.5" PointX="8.75" PointY="11.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Fornitori" Width="1.5" PointX="11.75" PointY="10.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest" Width="1.5" PointX="7.75" PointY="1.25" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Files" Width="1.5" PointX="12.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_FilesType" Width="1.5" PointX="2.75" PointY="12.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Iscrizione_Type" Width="1.5" PointX="4.75" PointY="13.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Progressivo_Ricevuta" Width="1.5" PointX="13.75" PointY="5.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_UserAdditionalDetails" Width="1.5" PointX="13.75" PointY="8.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Year" Width="1.5" PointX="0.75" PointY="1.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.GrestFatt" Width="1.5" PointX="13.75" PointY="11.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Groups" Width="1.5" PointX="14.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.InputOutput" Width="1.5" PointX="5.125" PointY="14.375" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Listino" Width="1.5" PointX="10.75" PointY="14.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Log" Width="1.5" PointX="15.75" PointY="4.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Logs" Width="1.5" PointX="15.75" PointY="9.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Mensa" Width="1.5" PointX="12.75" PointY="15.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.News" Width="1.5" PointX="2.75" PointY="5" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Pagamenti" Width="1.5" PointX="15.75" PointY="13.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.PayPalConfig" Width="1.5" PointX="10" PointY="2.875" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Prodotti" Width="1.5" PointX="16.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Province" Width="1.5" PointX="2.75" PointY="17.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Regioni" Width="1.5" PointX="17.75" PointY="3.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Activity" Width="1.5" PointX="17.75" PointY="9.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Groups" Width="1.5" PointX="17.75" PointY="12.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_News" Width="1.5" PointX="5" PointY="5.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Trip_Bus" Width="1.5" PointX="17.75" PointY="15.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Trip_Bus" Width="1.5" PointX="4.75" PointY="18.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.TripType" Width="1.5" PointX="18.75" PointY="0.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Users" Width="1.5" PointX="0.75" PointY="7" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.Users_AdditionalDetails" Width="1.5" PointX="8.75" PointY="18.75" IsExpanded="true" />
        <EntityTypeShape EntityType="iGrestModel.UserType" Width="1.5" PointX="0.75" PointY="4.625" IsExpanded="true" />
        <AssociationConnector Association="iGrestModel.FK_PayPalConfig_Grest" ManuallyRouted="false" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_News_News" ManuallyRouted="false" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Bar" />
        <AssociationConnector Association="iGrestModel.FK_Grest_UserAdditionalDetails_Grest" />
        <AssociationConnector Association="iGrestModel.FK_GrestFatt_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Grest_FilesType" />
        <AssociationConnector Association="iGrestModel.FK_Listino_Listino" />
        <AssociationConnector Association="iGrestModel.FK_Pagamenti_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_News_Users" />
        <AssociationConnector Association="iGrestModel.FK_Users_AdditionalDetails_Users" />
        <EntityTypeShape EntityType="iGrestModel.NotificType" Width="1.5" PointX="8.75" PointY="25" />
        <EntityTypeShape EntityType="iGrestModel.REL_Users_NotificPlayers" Width="1.5" PointX="11" PointY="19.75" />
        <AssociationConnector Association="iGrestModel.FK_REL_Users_NotificPlayers_NotificType" />
        <AssociationConnector Association="iGrestModel.FK_REL_Users_NotificPlayers_Users" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Files_Users" Width="1.5" PointX="20" PointY="8.875" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Users_Grest_Files" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Users_Users" />
        <EntityTypeShape EntityType="iGrestModel.GrestHeadquarters" Width="1.5" PointX="10" PointY="0.75" />
        <AssociationConnector Association="iGrestModel.FK_GrestHeadquarters_Grest" />
        <AssociationConnector Association="iGrestModel.FK_InputOutput_GrestHeadquarters" />
        <AssociationConnector Association="iGrestModel.FK_Mensa_GrestHeadquarters" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Headquarter" Width="1.5" PointX="20.25" PointY="5.875" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Headquarter_GrestHeadquarters" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Headquarter_Users" />
        <EntityTypeShape EntityType="iGrestModel.TransactionType" Width="1.5" PointX="20.375" PointY="11.75" />
        <EntityTypeShape EntityType="iGrestModel.News_Read" Width="1.5" PointX="14" PointY="21.125" />
        <AssociationConnector Association="iGrestModel.FK_News_Read_News" />
        <AssociationConnector Association="iGrestModel.FK_News_Read_Users" />
        <EntityTypeShape EntityType="iGrestModel.Order" Width="1.5" PointX="20" PointY="15" />
        <EntityTypeShape EntityType="iGrestModel.OrderItem" Width="1.5" PointX="23.25" PointY="8.125" />
        <AssociationConnector Association="iGrestModel.FK_Order_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Order_Users" />
        <AssociationConnector Association="iGrestModel.FK_OrderItem_Order" />
        <AssociationConnector Association="iGrestModel.FK_OrderItem_Prodotti" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Order" />
        <AssociationConnector Association="iGrestModel.FK_Order_Grest_Year" />
        <EntityTypeShape EntityType="iGrestModel.Triage" Width="1.5" PointX="12.75" PointY="2.125" />
        <EntityTypeShape EntityType="iGrestModel.Triage_Categorie" Width="1.5" PointX="9.375" PointY="32.125" />
        <AssociationConnector Association="iGrestModel.FK_Triage_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Triage_Triage_Categorie" />
        <EntityTypeShape EntityType="iGrestModel.Triage_InputOutput" Width="1.5" PointX="21.875" PointY="2.25" />
        <AssociationConnector Association="iGrestModel.FK_Triage_InputOutput_InputOutput" />
        <AssociationConnector Association="iGrestModel.FK_Triage_InputOutput_Triage" />
        <EntityTypeShape EntityType="iGrestModel.PaymentType" Width="1.5" PointX="0.75" PointY="21.875" />
        <EntityTypeShape EntityType="iGrestModel.ProdottiTipo" Width="1.5" PointX="14.5" PointY="24.375" />
        <AssociationConnector Association="iGrestModel.FK_Prodotti_ProdottiTipo" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Bus_Trip_Bus" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Bus_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Bus_UserType" />
        <AssociationConnector Association="iGrestModel.FK_GrestFatt_GrestHeadquarters" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Progressivo_Ricevuta_GrestHeadquarters" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Year" Width="1.5" PointX="3" PointY="0.875" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Year_Year" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Year_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Year_UserType" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Users" />
        <AssociationConnector Association="iGrestModel.FK_Activity_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Activity_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Activity_Activity" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Prodotti" />
        <AssociationConnector Association="iGrestModel.FK_Bar_Users" />
        <AssociationConnector Association="iGrestModel.FK_Comuni_Province" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Comuni" />
        <AssociationConnector Association="iGrestModel.FK_Users_Comuni" />
        <AssociationConnector Association="iGrestModel.FK_Dinners_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Mensa_Dinners" />
        <AssociationConnector Association="iGrestModel.FK_Fornitori_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Iscrizione_Type_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Progressivo_Ricevuta_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Province" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Year_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Groups_Grest" />
        <AssociationConnector Association="iGrestModel.FK_InputOutput_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Listino_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Mensa_Grest" />
        <AssociationConnector Association="iGrestModel.FK_News_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Prodotti_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Users_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Progressivo_Ricevuta_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Groups_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_InputOutput_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Listino_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Mensa_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Users_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Groups_Groups" />
        <AssociationConnector Association="iGrestModel.FK_InputOutput_Users" />
        <AssociationConnector Association="iGrestModel.FK_Listino_Prodotti" />
        <AssociationConnector Association="iGrestModel.FK_Listino_Users" />
        <AssociationConnector Association="iGrestModel.FK_Mensa_Users" />
        <AssociationConnector Association="iGrestModel.FK_Province_Regioni" />
        <AssociationConnector Association="iGrestModel.FK_Users_Province" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Activity_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Activity_UserType" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Groups_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Groups_UserType" />
        <AssociationConnector Association="iGrestModel.FK_Users_Users" />
        <EntityTypeShape EntityType="iGrestModel.Portale" Width="1.5" PointX="17.375" PointY="22.75" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Portale" />
        <AssociationConnector Association="iGrestModel.FK_Logs_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Logs_Users" />
        <EntityTypeShape EntityType="iGrestModel.StripeConfig" Width="1.5" PointX="10" PointY="27.5" />
        <AssociationConnector Association="iGrestModel.FK_StripeConfig_Grest" />
        <EntityTypeShape EntityType="iGrestModel.StripeTransaction" Width="1.5" PointX="3.375" PointY="22.75" />
        <AssociationConnector Association="iGrestModel.BalanceSheetsW_BalanceUsers" />
        <EntityTypeShape EntityType="iGrestModel.FE_CondizioniPagamento" Width="1.5" PointX="20.75" PointY="37.25" />
        <EntityTypeShape EntityType="iGrestModel.FE_FormatoTrasmissione" Width="1.5" PointX="20.75" PointY="42.75" />
        <EntityTypeShape EntityType="iGrestModel.FE_ModalitaPagamento" Width="1.5" PointX="20.75" PointY="40" />
        <EntityTypeShape EntityType="iGrestModel.FE_RegimeFiscale" Width="1.5" PointX="11.5" PointY="22.625" />
        <EntityTypeShape EntityType="iGrestModel.FE_TipoDocumento" Width="1.5" PointX="20.75" PointY="45.5" />
        <AssociationConnector Association="iGrestModel.FK_GrestFatt_FE_RegimeFiscale" />
        <EntityTypeShape EntityType="iGrestModel.BalanceIncomeStatements" Width="1.5" PointX="10" PointY="35" />
        <AssociationConnector Association="iGrestModel.FK_BalanceIncomeStatements_Grest" />
        <AssociationConnector Association="iGrestModel.FK_InputOutput_UsersCreated" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_Users1" />
        <EntityTypeShape EntityType="iGrestModel.SchoolClass" Width="1.5" PointX="21.375" PointY="19.875" />
        <AssociationConnector Association="iGrestModel.FK_Users_SchoolClass" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_TripsPrice" Width="1.5" PointX="29.5" PointY="15.125" />
        <EntityTypeShape EntityType="iGrestModel.TripsPrice" Width="1.5" PointX="27.25" PointY="3.875" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_TripsPrice_TripsPrice" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_TripsPrice_Users" />
        <AssociationConnector Association="iGrestModel.FK_TripsPrice_UserType" />
        <EntityTypeShape EntityType="iGrestModel.SatispayConfig" Width="1.5" PointX="24.375" PointY="0.75" />
        <AssociationConnector Association="iGrestModel.FK_SatispayConfig_Grest" />
        <EntityTypeShape EntityType="iGrestModel.SatispayTransaction" Width="1.5" PointX="24.375" PointY="4.75" />
        <EntityTypeShape EntityType="iGrestModel.Grest_Document" Width="1.5" PointX="23" PointY="13.25" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Document_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Document_Users" />
        <EntityTypeShape EntityType="iGrestModel.BalanceSheets" Width="1.5" PointX="26.625" PointY="13.125" />
        <EntityTypeShape EntityType="iGrestModel.W_BalanceUsers" Width="1.5" PointX="3.375" PointY="25.75" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_BalanceIncomeStatements" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_Grest" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_GrestHeadquarters" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_PaymentType" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_TransactionType" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_Users" />
        <AssociationConnector Association="iGrestModel.FK_BalanceSheets_Users1" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Files_BalanceSheets" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_TripsPrice_BalanceSheets" />
        <AssociationConnector Association="iGrestModel.FK_Grest_Document_GrestHeadquarters" />
        <EntityTypeShape EntityType="iGrestModel.BalanceReceiptDetails" Width="1.5" PointX="28.875" PointY="21.25" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_BalanceSheets" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_FE_CondizioniPagamento" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_FE_FormatoTrasmissione" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_FE_ModalitaPagamento" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_FE_TipoDocumento" />
        <AssociationConnector Association="iGrestModel.FK_BalanceReceiptDetails_Grest_Files" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Training" Width="1.5" PointX="26.5" PointY="22.125" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Training_Users" />
        <EntityTypeShape EntityType="iGrestModel.Trainings" Width="1.5" PointX="30" PointY="4.25" />
        <AssociationConnector Association="iGrestModel.FK_Trainings_Grest1" />
        <AssociationConnector Association="iGrestModel.FK_Trainings_Grest_Year1" />
        <AssociationConnector Association="iGrestModel.FK_Trainings_GrestHeadquarters1" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Training_Trainings" />
        <AssociationConnector Association="iGrestModel.FK_Trainings_Trainings1" />
        <AssociationConnector Association="iGrestModel.Rel_Trainings_Groups" />
        <EntityTypeShape EntityType="iGrestModel.Rel_User_SubscriptionAttachments" Width="1.5" PointX="12.25" PointY="26.25" />
        <EntityTypeShape EntityType="iGrestModel.SubscriptionAttachments" Width="1.5" PointX="10" PointY="38.5" />
        <AssociationConnector Association="iGrestModel.FK_SubscriptionAttachments_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Rel_User_SubscriptionAttachments_SubscriptionAttachments" />
        <AssociationConnector Association="iGrestModel.FK_Rel_User_SubscriptionAttachments_Users" />
        <EntityTypeShape EntityType="iGrestModel.Sizes" Width="1.5" PointX="10" PointY="43.25" />
        <AssociationConnector Association="iGrestModel.FK_Sizes_Grest" />
        <EntityTypeShape EntityType="iGrestModel.ProdottiCategory" Width="1.5" PointX="10" PointY="46.25" />
        <AssociationConnector Association="iGrestModel.FK_ProdottiCategory_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Prodotti_ProdottiCategory" />
        <AssociationConnector Association="iGrestModel.FK_ProdottiCategory_ProdottiTipo" />
        <EntityTypeShape EntityType="iGrestModel.PrintableObject" Width="1.5" PointX="10" PointY="50.625" />
        <AssociationConnector Association="iGrestModel.FK_PrintableObject_Grest" />
        <EntityTypeShape EntityType="iGrestModel.GrestMenu" Width="1.5" PointX="19.375" PointY="22.75" />
        <EntityTypeShape EntityType="iGrestModel.GrestMenuCustom" Width="1.5" PointX="25.625" PointY="9.75" />
        <AssociationConnector Association="iGrestModel.FK_GrestMenuCustom_Grest" />
        <AssociationConnector Association="iGrestModel.FK_GrestMenuCustom_GrestMenu" />
        <EntityTypeShape EntityType="iGrestModel.BankTransferConfig" Width="1.5" PointX="10" PointY="54.125" />
        <AssociationConnector Association="iGrestModel.FK_BankTransferConfig_Grest" />
        <EntityTypeShape EntityType="iGrestModel.Configurations" Width="1.5" PointX="19.375" PointY="25.75" />
        <EntityTypeShape EntityType="iGrestModel.Temp_Table" Width="1.5" PointX="26.375" PointY="0.75" />
        <AssociationConnector Association="iGrestModel.FK_TripType_GrestMenu" />
        <AssociationConnector Association="iGrestModel.FK_GrestMenuCustom_GrestMenuCustom" />
        <EntityTypeShape EntityType="iGrestModel.UserToken" Width="1.5" PointX="14.375" PointY="27.75" />
        <AssociationConnector Association="iGrestModel.FK_UserToken_Users" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users_Trip" Width="1.5" PointX="17" PointY="28.5" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_UserType" />
        <EntityTypeShape EntityType="iGrestModel.Trips" Width="1.5" PointX="24" PointY="18.375" />
        <EntityTypeShape EntityType="iGrestModel.TripsMaster" Width="1.5" PointX="32" PointY="13.25" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_BalanceIncomeStatements" />
        <AssociationConnector Association="iGrestModel.FK_Trips_Grest" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_Grest" />
        <AssociationConnector Association="iGrestModel.FK_Trips_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_Grest_Year" />
        <AssociationConnector Association="iGrestModel.FK_Trips_TripType" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_GrestHeadquarters" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Trips" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Trip_Bus_Trips" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_TripsPrice_Trips" />
        <AssociationConnector Association="iGrestModel.FK_Trip_Bus_Trips" />
        <AssociationConnector Association="iGrestModel.FK_Trips_TripsMaster" />
        <AssociationConnector Association="iGrestModel.FK_Trips_TripType_1" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_TripsMaster" />
        <AssociationConnector Association="iGrestModel.FK_TripsMaster_TripType" />
        <AssociationConnector Association="iGrestModel.FK_TripsPrice_TripsMaster" />
        <AssociationConnector Association="iGrestModel.Rel_Trips_Groups" />
        <AssociationConnector Association="iGrestModel.FK_OrderItem_Sizes" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Files_Groups" Width="1.5" PointX="21" PointY="0.75" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Files_Groups_Grest_Files" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Files_Groups_Groups" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Files_Fornitori" Width="1.5" PointX="15" PointY="0.75" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Files_Fornitori_Fornitori" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Files_Fornitori_Grest_Files" />
        <EntityTypeShape EntityType="iGrestModel.MessageRead" Width="1.5" PointX="12.25" PointY="33.375" />
        <EntityTypeShape EntityType="iGrestModel.Messages" Width="1.5" PointX="10" PointY="58.125" />
        <AssociationConnector Association="iGrestModel.FK_MessageRead_Messages" />
        <AssociationConnector Association="iGrestModel.FK_MessageRead_Users" />
        <AssociationConnector Association="iGrestModel.Rel_Message_Grest" />
        <AssociationConnector Association="iGrestModel.Rel_Message_UserType" />
        <EntityTypeShape EntityType="iGrestModel.Rel_Users" Width="1.5" PointX="6" PointY="28.375" />
        <EntityTypeShape EntityType="iGrestModel.Rel_UsersType" Width="1.5" PointX="3.75" PointY="43.625" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Users" />
        <AssociationConnector Association="iGrestModel.FK_Rel_Users_Users_Parent" />
        <AssociationConnector Association="iGrestModel.FK_Rel_UsersType_ID_Type" />
        <AssociationConnector Association="iGrestModel.FK_Rel_UsersType_Rel_UsersType" />
        <EntityTypeShape EntityType="iGrestModel.TripsDiscountConditions" Width="1.5" PointX="34.25" PointY="28.375" />
        <AssociationConnector Association="iGrestModel.FK_TripsDiscountConditions_Rel_UsersType" />
        <AssociationConnector Association="iGrestModel.FK_TripsDiscountConditions_TripsMaster" />
        </Diagram>
    </edmx:Diagrams>
  </edmx:Designer>
</edmx:Edmx>