//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Grest_Files
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Grest_Files()
        {
            this.Grest_Files_Users = new HashSet<Grest_Files_Users>();
            this.BalanceReceiptDetails = new HashSet<BalanceReceiptDetails>();
            this.Rel_Files_Groups = new HashSet<Rel_Files_Groups>();
            this.Rel_Files_Fornitori = new HashSet<Rel_Files_Fornitori>();
        }
    
        public int ID_File { get; set; }
        public System.Guid ID_Grest { get; set; }
        public Nullable<System.DateTime> DataInsert { get; set; }
        public string FileType { get; set; }
        public string FileName { get; set; }
        public string FileExtension { get; set; }
        public string Title { get; set; }
        public string Text { get; set; }
        public Nullable<int> Position { get; set; }
        public Nullable<System.Guid> SharedWith { get; set; }
        public Nullable<System.DateTime> DateExpiration { get; set; }
        public int ID_FileType { get; set; }
        public Nullable<System.DateTime> DateDocument { get; set; }
        public Nullable<System.Guid> ID_Balance { get; set; }
        public Nullable<System.Guid> ID_User { get; set; }
        public bool Common { get; set; }
        public bool Private { get; set; }
        public bool Readed { get; set; }
        public Nullable<System.DateTime> ReadedDate { get; set; }
    
        public virtual Grest_FilesType Grest_FilesType { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files_Users> Grest_Files_Users { get; set; }
        public virtual Users Users { get; set; }
        public virtual Grest Grest { get; set; }
        public virtual Users Users1 { get; set; }
        public virtual BalanceSheets BalanceSheets { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceReceiptDetails> BalanceReceiptDetails { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Files_Groups> Rel_Files_Groups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Files_Fornitori> Rel_Files_Fornitori { get; set; }
    }
}
