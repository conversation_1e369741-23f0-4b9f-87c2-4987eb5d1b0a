//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class BalanceIncomeStatements
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public BalanceIncomeStatements()
        {
            this.BalanceSheets = new HashSet<BalanceSheets>();
            this.TripsMaster = new HashSet<TripsMaster>();
        }
    
        public System.Guid ID_IncomeStatement { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public System.Guid ID_Grest { get; set; }
        public bool Enabled { get; set; }
    
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceSheets> BalanceSheets { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsMaster> TripsMaster { get; set; }
    }
}
