insert into [dbo].<PERSON><PERSON> (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '3-4 anni', '<PERSON>mbini 3-4 anni', 0
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '3-4 anni'
where s.ID_Size is null

insert into [dbo].<PERSON><PERSON> (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '4-6 anni', 'Bambini 4-6 anni', 1
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '4-6 anni'
where s.ID_Size is null

insert into [dbo].<PERSON>zes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '6-8 anni', 'Bambini 6-8 anni', 2
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '6-8 anni'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '8-10 anni', '<PERSON>mbini 8-10 anni', 3
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '8-10 anni'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '10-12 anni', 'Bambini 10-12 anni', 4
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '10-12 anni'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '12-14 anni', 'Bambini 12-14 anni', 5
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '12-14 anni'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'XXS', 'Adulti XXS', 6
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'XXS'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'XS', 'Adulti XS', 7
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'XS'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'S', 'Adulti S', 8
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'S'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'M', 'Adulti M', 9
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'M'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'L', 'Adulti L', 10
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'L'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, 'XL', 'Adulti XL', 11
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = 'XL'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '2XL', 'Adulti 2XL', 12
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '2XL'
where s.ID_Size is null

insert into [dbo].Sizes (ID_Grest, Name, Label, Sort)
select g.ID_Grest, '3XL', 'Adulti 3XL', 13
from [dbo].Grest g
left join [dbo].Sizes s on s.ID_Grest = g.ID_Grest and s.Name = '3XL'
where s.ID_Size is null
/*
<option value="3-4 anni">Bambini 3-4 anni</option>
<option value="4-6 anni">Bambini 4-6 anni</option>
<option value="6-8 anni">Bambini 6-8 anni</option>
<option value="8-10 anni">Bambini 8-10 anni</option>
<option value="10-12 anni">Bambini 10-12 anni</option>
<option value="12-14 anni">Bambini 12-14 anni</option>
<option value="XXS">Adulti XXS</option>
<option value="XS">Adulti XS</option>
<option value="S">Adulti S</option>
<option value="M">Adulti M</option>
<option value="L">Adulti L</option>
<option value="XL">Adulti XL</option>
<option value="2XL">Adulti 2XL</option>
<option value="3XL">Adulti 3XL</option>
*/