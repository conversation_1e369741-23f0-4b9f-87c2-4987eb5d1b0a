select u.ID_User, Lastname, Name, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Father, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, 
p.<PERSON> as '<PERSON><PERSON><PERSON>', 
c.<PERSON> as '<PERSON><PERSON><PERSON>', c<PERSON><PERSON>,
MedicalInfo, MedicalCare, SchoolName, SchoolClass, SchoolCity, Size, Note, Conto, Weeks, InsuranceN, ParentCf, ChildCf,
u.Cap as 'Cap Residenza', u.<PERSON>, u.Pro<PERSON>cia, u.CognomeGenitoreFiscale, u.NomeGenitoreFiscale, u.IndirizzoGenitoreFiscale,
u.CapGenitoreFiscale, u.ProvinciaGenitoreFiscale, u.CfGenitoreFiscale, u.TempoP<PERSON>oParziale, u.NoteRiservate, u.LimiteGiornalieroBar, u.Country, 
u.Extra1, u.Extra2, u.Extra3, u.DataComunione, u.LuogoComunione, u.DataBattesimo, u.<PERSON>attesi<PERSON>, u.<PERSON>sp<PERSON>, u.<PERSON>, u.Battesimo<PERSON>, 
u<PERSON>, u<PERSON>, u<PERSON>, u.DataCresima, u.Lu<PERSON>res<PERSON>, u.NominativoPadrino, u.NominativoMadrina, u.IndirizzoPadrino, u.IndirizzoMadrina,
u.CartaIdentita, u.TesseraSanitaria, u.CardN, u.TripDiscountPerc
from [dbo].Users u
inner join [dbo].Rel_Users_Year ruy on u.ID_User = ruy.ID_User
left join [dbo].Comuni c on u.ID_Comune = c.ID_Comune
left join [dbo].Province p on u.ID_Provincia = p.ID_Provincia
where ruy.Enabled = 1 and ruy.Removed = 0 and u.ID_Grest = 'CFCE32A2-36BE-424D-9608-E2FD489F8483'