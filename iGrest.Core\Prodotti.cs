//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Prodotti
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Prodotti()
        {
            this.OrderItem = new HashSet<OrderItem>();
            this.Bar = new HashSet<Bar>();
            this.Listino = new HashSet<Listino>();
        }
    
        public int ID_Prodotto { get; set; }
        public System.Guid ID_Grest { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
        public bool Removed { get; set; }
        public int Tipo { get; set; }
        public decimal Quantity { get; set; }
        public string Image { get; set; }
        public bool ManageWarehouse { get; set; }
        public string CssClass { get; set; }
        public Nullable<System.Guid> ID_Category { get; set; }
        public bool ManageSize { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<OrderItem> OrderItem { get; set; }
        public virtual ProdottiTipo ProdottiTipo { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Bar> Bar { get; set; }
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Listino> Listino { get; set; }
        public virtual ProdottiCategory ProdottiCategory { get; set; }
    }
}
