//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Order
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Order()
        {
            this.OrderItem = new HashSet<OrderItem>();
            this.Bar = new HashSet<Bar>();
        }
    
        public int ID_Order { get; set; }
        public System.Guid ID_User { get; set; }
        public System.Guid ID_Grest { get; set; }
        public byte Status { get; set; }
        public bool Removed { get; set; }
        public decimal TotalAmount { get; set; }
        public int ID_Year { get; set; }
        public System.DateTime DateInsert { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Users Users { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<OrderItem> OrderItem { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Bar> Bar { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
    }
}
