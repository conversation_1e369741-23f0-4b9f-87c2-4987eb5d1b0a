-- =====================================================
-- SCRIPT DI OTTIMIZZAZIONE DATABASE iGrest
-- Data: 07/06/2025
-- Scopo: Ottimizzare le dimensioni dei database dopo l'analisi comparativa
-- =====================================================

USE master;
GO

PRINT '=== INIZIO OTTIMIZZAZIONE DATABASE iGrest ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. BACKUP E SHRINK DEL LOG DEL DATABASE DI SVILUPPO
-- =====================================================

PRINT '1. Ottimizzazione Database di Sviluppo (iGrest)'
PRINT '---------------------------------------------------'

-- Verifica che il database esista
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'iGrest')
BEGIN
    PRINT '✓ Database iGrest trovato'
    
    -- Cambia al database di sviluppo
    USE iGrest;
    
    -- Verifica lo spazio utilizzato prima dell'ottimizzazione
    PRINT 'Dimensioni PRIMA dell''ottimizzazione:'
    SELECT 
        name AS FileName,
        type_desc AS FileType,
        CAST(size * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS SizeMB,
        CAST(FILEPROPERTY(name, 'SpaceUsed') * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS UsedMB
    FROM sys.database_files;
    
    -- Backup del log delle transazioni
    PRINT 'Esecuzione backup del log delle transazioni...'
    DECLARE @BackupPath NVARCHAR(500) = 'C:\temp\iGrest_log_backup_' + REPLACE(CONVERT(VARCHAR, GETDATE(), 120), ':', '') + '.bak'
    DECLARE @BackupSQL NVARCHAR(1000) = 'BACKUP LOG iGrest TO DISK = ''' + @BackupPath + ''''
    
    BEGIN TRY
        EXEC sp_executesql @BackupSQL
        PRINT '✓ Backup del log completato: ' + @BackupPath
    END TRY
    BEGIN CATCH
        PRINT '⚠ Errore durante il backup del log: ' + ERROR_MESSAGE()
        PRINT 'Tentativo di backup completo del database...'
        
        SET @BackupPath = 'C:\temp\iGrest_full_backup_' + REPLACE(CONVERT(VARCHAR, GETDATE(), 120), ':', '') + '.bak'
        SET @BackupSQL = 'BACKUP DATABASE iGrest TO DISK = ''' + @BackupPath + ''''
        EXEC sp_executesql @BackupSQL
        PRINT '✓ Backup completo del database completato: ' + @BackupPath
    END CATCH
    
    -- Shrink del file di log
    PRINT 'Riduzione del file di log...'
    BEGIN TRY
        DBCC SHRINKFILE('iGrest_log', 100);
        PRINT '✓ Riduzione del file di log completata'
    END TRY
    BEGIN CATCH
        PRINT '⚠ Errore durante la riduzione del file di log: ' + ERROR_MESSAGE()
    END CATCH
    
    -- Verifica lo spazio utilizzato dopo l'ottimizzazione
    PRINT 'Dimensioni DOPO l''ottimizzazione:'
    SELECT 
        name AS FileName,
        type_desc AS FileType,
        CAST(size * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS SizeMB,
        CAST(FILEPROPERTY(name, 'SpaceUsed') * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS UsedMB
    FROM sys.database_files;
    
END
ELSE
BEGIN
    PRINT '✗ Database iGrest non trovato'
END

PRINT ''

-- =====================================================
-- 2. OTTIMIZZAZIONE GENERALE DEI DATABASE
-- =====================================================

PRINT '2. Ottimizzazione Generale Database'
PRINT '-----------------------------------'

-- Ottimizzazione database di produzione
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'iGrest')
BEGIN
    PRINT 'Ottimizzazione database di produzione (iGrest)...'
    USE iGrest;
    
    -- Aggiorna le statistiche
    PRINT 'Aggiornamento statistiche...'
    EXEC sp_updatestats;
    PRINT '✓ Statistiche aggiornate'
    
    -- Riorganizza gli indici frammentati
    PRINT 'Riorganizzazione indici...'
    DECLARE @sql NVARCHAR(MAX) = ''
    SELECT @sql = @sql + 'ALTER INDEX ALL ON [' + SCHEMA_NAME(schema_id) + '].[' + name + '] REORGANIZE;' + CHAR(13)
    FROM sys.tables 
    WHERE is_ms_shipped = 0
    
    BEGIN TRY
        EXEC sp_executesql @sql
        PRINT '✓ Indici riorganizzati'
    END TRY
    BEGIN CATCH
        PRINT '⚠ Errore durante la riorganizzazione degli indici: ' + ERROR_MESSAGE()
    END CATCH
END

-- Ottimizzazione database di sviluppo
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'iGrest')
BEGIN
    PRINT 'Ottimizzazione database di sviluppo (iGrest)...'
    USE iGrest;
    
    -- Aggiorna le statistiche
    PRINT 'Aggiornamento statistiche...'
    EXEC sp_updatestats;
    PRINT '✓ Statistiche aggiornate'
    
    -- Riorganizza gli indici frammentati
    PRINT 'Riorganizzazione indici...'
    DECLARE @sql2 NVARCHAR(MAX) = ''
    SELECT @sql2 = @sql2 + 'ALTER INDEX ALL ON [' + SCHEMA_NAME(schema_id) + '].[' + name + '] REORGANIZE;' + CHAR(13)
    FROM sys.tables 
    WHERE is_ms_shipped = 0
    
    BEGIN TRY
        EXEC sp_executesql @sql2
        PRINT '✓ Indici riorganizzati'
    END TRY
    BEGIN CATCH
        PRINT '⚠ Errore durante la riorganizzazione degli indici: ' + ERROR_MESSAGE()
    END CATCH
END

PRINT ''

-- =====================================================
-- 3. REPORT FINALE DELLE DIMENSIONI
-- =====================================================

PRINT '3. Report Finale delle Dimensioni'
PRINT '----------------------------------'

-- Report database di produzione
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'iGrest')
BEGIN
    USE iGrest;
    PRINT 'DATABASE: iGrest (Produzione)'
    SELECT 
        'iGrest' AS DatabaseName,
        CAST(SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024 AS DECIMAL(15,2)) AS UsedSpaceMB,
        CAST(SUM(size) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS AllocatedSpaceMB,
        CAST((SUM(size) * 8192. / 1024 / 1024) - (SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024) AS DECIMAL(15,2)) AS FreeSpaceMB
    FROM sys.database_files;
END

-- Report database di sviluppo
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'iGrest')
BEGIN
    USE iGrest;
    PRINT 'DATABASE: iGrest (Sviluppo)'
    SELECT 
        'iGrest' AS DatabaseName,
        CAST(SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024 AS DECIMAL(15,2)) AS UsedSpaceMB,
        CAST(SUM(size) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS AllocatedSpaceMB,
        CAST((SUM(size) * 8192. / 1024 / 1024) - (SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024) AS DECIMAL(15,2)) AS FreeSpaceMB
    FROM sys.database_files;
END

-- =====================================================
-- 4. RACCOMANDAZIONI PER LA MANUTENZIONE FUTURA
-- =====================================================

PRINT ''
PRINT '4. Raccomandazioni per la Manutenzione Futura'
PRINT '----------------------------------------------'
PRINT '• Eseguire questo script mensilmente'
PRINT '• Monitorare la crescita dei file di log'
PRINT '• Considerare l''archiviazione di dati storici nelle tabelle Logs e InputOutput'
PRINT '• Implementare backup automatici differenziali'
PRINT '• Valutare l''implementazione di un piano di manutenzione automatico'

PRINT ''
PRINT '=== OTTIMIZZAZIONE COMPLETATA ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)

-- Torna al database master
USE master;
GO