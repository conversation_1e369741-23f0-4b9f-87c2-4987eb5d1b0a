using iGrest.Core;
using iGrest.Jobs.Loggers;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;

namespace iGrest.Jobs.Commands
{
	public class LogCleanupCommand : ICommand
	{
		private readonly DBLogger _logger;

		public LogCleanupCommand(ILoggerFactory loggerFactory)
		{
			_logger = new DBLogger(loggerFactory);
		}

		public void Execute(string[] args)
		{
			try
			{
				_logger.Log("Starting log cleanup process");

				using (var db = new iGrestEntities())
				{
					// CLEANUP STRATEGICO: Rimuovi log più vecchi di 30 giorni
					var cutoffDate = DateTime.Now.AddDays(-30);
					
					// Conta i log da rimuovere
					var logsToDelete = db.Log.Where(x => x.DTINSERT < cutoffDate).Count();
					
					if (logsToDelete > 0)
					{
						_logger.Log($"Found {logsToDelete} old log entries to delete");
						
						// Rimuovi in batch per evitare timeout
						var batchSize = 1000;
						var totalDeleted = 0;
						
						while (true)
						{
							var batch = db.Log
								.Where(x => x.DTINSERT < cutoffDate)
								.Take(batchSize)
								.ToList();
								
							if (!batch.Any())
								break;
								
							db.Log.RemoveRange(batch);
							db.SaveChanges();
							
							totalDeleted += batch.Count;
							_logger.Log($"Deleted {batch.Count} log entries (total: {totalDeleted})");
							
							// Pausa per non sovraccaricare il database
							System.Threading.Thread.Sleep(100);
						}
						
						_logger.Log($"Log cleanup completed. Total deleted: {totalDeleted}");
					}
					else
					{
						_logger.Log("No old log entries found to delete");
					}

					// CLEANUP LOGS TABELLA (nuova struttura)
					var logsNewToDelete = db.Logs.Where(x => x.DataInsert < cutoffDate).Count();
					
					if (logsNewToDelete > 0)
					{
						_logger.Log($"Found {logsNewToDelete} old Logs entries to delete");
						
						var batchSize = 1000;
						var totalDeleted = 0;
						
						while (true)
						{
							var batch = db.Logs
								.Where(x => x.DataInsert < cutoffDate)
								.Take(batchSize)
								.ToList();
								
							if (!batch.Any())
								break;
								
							db.Logs.RemoveRange(batch);
							db.SaveChanges();
							
							totalDeleted += batch.Count;
							_logger.Log($"Deleted {batch.Count} Logs entries (total: {totalDeleted})");
							
							System.Threading.Thread.Sleep(100);
						}
						
						_logger.Log($"Logs cleanup completed. Total deleted: {totalDeleted}");
					}

					// CLEANUP STRIPE TRANSACTIONS VECCHIE (più di 90 giorni)
					var stripeCleanupDate = DateTime.Now.AddDays(-90);
					var stripeToDelete = db.StripeTransaction.Where(x => x.Transaction_Date < stripeCleanupDate).Count();
					
					if (stripeToDelete > 0)
					{
						_logger.Log($"Found {stripeToDelete} old Stripe transactions to delete");
						
						var batch = db.StripeTransaction
							.Where(x => x.Transaction_Date < stripeCleanupDate)
							.Take(500)
							.ToList();
							
						if (batch.Any())
						{
							db.StripeTransaction.RemoveRange(batch);
							db.SaveChanges();
							_logger.Log($"Deleted {batch.Count} old Stripe transactions");
						}
					}
				}

				_logger.Log("Log cleanup process completed successfully");
			}
			catch (Exception ex)
			{
				_logger.Log($"Error during log cleanup: {ex.Message}");
				throw;
			}
		}
	}
}