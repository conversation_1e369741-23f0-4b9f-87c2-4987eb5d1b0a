﻿using iGrest.Core;
using iGrest.Jobs.Commands;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace iGrest.Jobs.Loggers
{
	public class DBLogger
	{
		private readonly ILogger _logger;
		private readonly Guid ID_Grest = new Guid("047DD625-6C04-4ECE-9CF5-628048A99E67");
		private readonly string GrestName = "iGrest.Jobs";

		// OTTIMIZZAZIONE: Batch logging per ridurre il carico database
		private readonly ConcurrentQueue<Logs> _logQueue = new ConcurrentQueue<Logs>();
		private readonly Timer _flushTimer;
		private readonly object _flushLock = new object();

		public DBLogger(ILoggerFactory loggerFactory)
		{
			_logger = loggerFactory.CreateLogger<DBLogger>();
			
			// Flush dei log ogni 30 secondi o quando ci sono 10+ messaggi
			_flushTimer = new Timer(FlushLogs, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
		}

		public void Log(string message)
		{
			try
			{
				// OTTIMIZZAZIONE: Aggiungi alla coda invece di scrivere immediatamente
				_logQueue.Enqueue(new Logs
				{
					Action = message,
					DataInsert = DateTime.Now,
					ID_Grest = ID_Grest,
					GrestName = GrestName,
					Lastname = GrestName
				});

				// Se la coda è piena, forza il flush
				if (_logQueue.Count >= 10)
				{
					Task.Run(() => FlushLogs(null));
				}
			}
			catch (Exception ex)
			{
				_logger.LogError("Error to queue log - message=" + message + " | ex=" + ex);
			}
		}

		private void FlushLogs(object state)
		{
			if (_logQueue.IsEmpty)
				return;

			lock (_flushLock)
			{
				try
				{
					var logsToFlush = new List<Logs>();
					
					// Estrai tutti i log dalla coda
					while (_logQueue.TryDequeue(out var log) && logsToFlush.Count < 50) // Max 50 per batch
					{
						logsToFlush.Add(log);
					}

					if (logsToFlush.Any())
					{
						using (var db = new iGrestEntities())
						{
							// OTTIMIZZAZIONE: Batch insert
							db.Configuration.AutoDetectChangesEnabled = false;
							db.Configuration.ValidateOnSaveEnabled = false;

							foreach (var log in logsToFlush)
							{
								db.Logs.Add(log);
							}

							db.SaveChanges();
						}

						_logger.LogInformation($"Flushed {logsToFlush.Count} log entries to database");
					}
				}
				catch (Exception ex)
				{
					_logger.LogError("Error flushing logs to database: " + ex.Message);
					
					// Rimetti i log nella coda se il flush fallisce
					// (solo per i primi 10 per evitare loop infiniti)
					// foreach (var log in logsToFlush.Take(10))
					// {
					//     _logQueue.Enqueue(log);
					// }
				}
			}
		}

		public void Dispose()
		{
			_flushTimer?.Dispose();
			FlushLogs(null); // Flush finale
		}
	}
}
