delete [dbo].Rel_Users_Trip where ID_<PERSON> in (
	select ID_Trip
	from [dbo].Trips
	where Removed = 1 and EndDate < '2025-01-01'

	union

	select ID_Trip
	from [dbo].Trips
	where ID_TripMaster in (
		select ID_TripMaster
		from [dbo].TripsMaster
		where Removed = 1 and EndDate < '2025-01-01'
	)
)

delete from [dbo].Rel_Trips_Groups where ID_Trip in (
	select ID_Trip
	from [dbo].Trips
	where Removed = 1 and EndDate < '2025-01-01'

	union

	select ID_Trip
	from [dbo].Trips
	where ID_TripMaster in (
		select ID_TripMaster
		from [dbo].TripsMaster
		where Removed = 1 and EndDate < '2025-01-01'
	)
)

delete from [dbo].Rel_Users_TripsPrice where ID_Trip in (
	select ID_Trip
	from [dbo].Trips
	where Removed = 1 and EndDate < '2025-01-01'

	union

	select ID_Trip
	from [dbo].Trips
	where ID_TripMaster in (
		select ID_TripMaster
		from [dbo].TripsMaster
		where Removed = 1 and EndDate < '2025-01-01'
	)
)

delete from [dbo].TripsPrice where ID_TripMaster in (
	select ID_TripMaster
	from [dbo].TripsMaster
	where Removed = 1 and EndDate < '2025-01-01'
)

delete from [dbo].TripsDiscountConditions where ID_TripMaster in (
	select ID_TripMaster
	from [dbo].TripsMaster
	where Removed = 1 and EndDate < '2025-01-01'
)

delete from [dbo].Rel_Users_Trip_Bus where ID_Trip in (
	select ID_Trip
	from [dbo].Trips
	where Removed = 1 and EndDate < '2025-01-01'

	union

	select ID_Trip
	from [dbo].Trips
	where ID_TripMaster in (
		select ID_TripMaster
		from [dbo].TripsMaster
		where Removed = 1 and EndDate < '2025-01-01'
	)
)

delete from [dbo].Trip_Bus where ID_Trip in (
	select ID_Trip
	from [dbo].Trips
	where Removed = 1 and EndDate < '2025-01-01'

	union

	select ID_Trip
	from [dbo].Trips
	where ID_TripMaster in (
		select ID_TripMaster
		from [dbo].TripsMaster
		where Removed = 1 and EndDate < '2025-01-01'
	)
)

update [dbo].TripsMaster set MandatorySubscriptionTripMasterID = null where MandatorySubscriptionTripMasterID in (	
	select ID_TripMaster
	from [dbo].TripsMaster
	where Removed = 1 and EndDate < '2025-01-01'
)

delete from [dbo].Trips where ID_TripMaster in (
	select ID_TripMaster
	from [dbo].TripsMaster
	where Removed = 1 and EndDate < '2025-01-01'
)

delete from [dbo].Trips where Removed = 1 and EndDate < '2025-01-01'

delete from [dbo].TripsMaster where Removed = 1 and EndDate < '2025-01-01'