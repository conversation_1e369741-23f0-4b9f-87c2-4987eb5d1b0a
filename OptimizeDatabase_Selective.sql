-- =====================================================
-- SCRIPT DI OTTIMIZZAZIONE SELETTIVA DATABASE iGrest
-- Data: 07/06/2025
-- Scopo: Pulire SOLO i log di errori/debug, mantenere i log degli accessi utente
-- =====================================================

USE iGrest;
GO

PRINT '=== OTTIMIZZAZIONE SELETTIVA DATABASE iGrest ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. ANALISI SITUAZIONE ATTUALE
-- =====================================================

PRINT '1. SITUAZIONE ATTUALE'
PRINT '---------------------'

-- Conteggio record attuali
SELECT 'Logs (accessi utente)' as Tabella, COUNT(*) as Record FROM Logs
UNION ALL
SELECT 'Log (errori/debug)' as Tabella, COUNT(*) as Record FROM Log;

-- Dimensioni file attuali
SELECT 
    name as NomeFile,
    type_desc as Tipo,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as UsatoMB
FROM sys.database_files;

PRINT ''

-- =====================================================
-- 2. ANALISI ETÀ DEI LOG DI ERRORE
-- =====================================================

PRINT '2. ANALISI ETÀ DEI LOG DI ERRORE (tabella Log)'
PRINT '-----------------------------------------------'

-- Analisi solo tabella Log (errori/debug) - usa DTINSERT
SELECT 
    CASE 
        WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END as Periodo,
    COUNT(*) as NumeroRecord,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Log) as Percentuale
FROM Log 
WHERE DTINSERT IS NOT NULL
GROUP BY 
    CASE 
        WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
        WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
        WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
        WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
        ELSE 'Più di 1 anno'
    END
ORDER BY 
    CASE 
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 7 giorni' THEN 1
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 30 giorni' THEN 2
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimi 90 giorni' THEN 3
        WHEN CASE 
            WHEN DTINSERT >= DATEADD(day, -7, GETDATE()) THEN 'Ultimi 7 giorni'
            WHEN DTINSERT >= DATEADD(day, -30, GETDATE()) THEN 'Ultimi 30 giorni'
            WHEN DTINSERT >= DATEADD(day, -90, GETDATE()) THEN 'Ultimi 90 giorni'
            WHEN DTINSERT >= DATEADD(day, -365, GETDATE()) THEN 'Ultimo anno'
            ELSE 'Più di 1 anno'
        END = 'Ultimo anno' THEN 4
        ELSE 5
    END;

PRINT ''

-- Mostra alcuni esempi di log di errore per conferma
PRINT 'Esempi di log di errore (primi 5 record):'
SELECT TOP 5 
    ID,
    LEFT(ISNULL(message, 'N/A'), 100) as Messaggio,
    LEFT(ISNULL(pagina, 'N/A'), 50) as Pagina,
    DTINSERT
FROM Log 
ORDER BY DTINSERT DESC;

PRINT ''

-- =====================================================
-- 3. PULIZIA SELETTIVA - SOLO LOG DI ERRORE VECCHI
-- =====================================================

PRINT '3. PULIZIA SELETTIVA - SOLO LOG DI ERRORE'
PRINT '-----------------------------------------'

-- Conta quanti record verranno eliminati SOLO dalla tabella Log
DECLARE @LogToDelete INT

SELECT @LogToDelete = COUNT(*) FROM Log WHERE DTINSERT < DATEADD(day, -90, GETDATE())

PRINT 'Record da eliminare:'
PRINT '  - Tabella Logs (accessi utente): 0 record (MANTENUTI TUTTI)'
PRINT '  - Tabella Log (errori/debug): ' + CAST(@LogToDelete AS VARCHAR) + ' record (più vecchi di 90 giorni)'

-- Conferma prima di procedere
PRINT ''
PRINT '⚠ ATTENZIONE: Verranno eliminati SOLO i log di errore/debug più vecchi di 90 giorni'
PRINT '⚠ I log degli accessi utente (tabella Logs) verranno MANTENUTI TUTTI'
PRINT ''

-- Elimina SOLO log di errore più vecchi di 90 giorni dalla tabella Log
PRINT 'Eliminazione log di errore vecchi dalla tabella Log...'
DELETE FROM Log WHERE DTINSERT < DATEADD(day, -90, GETDATE())
PRINT '✓ Eliminati ' + CAST(@@ROWCOUNT AS VARCHAR) + ' record di errore dalla tabella Log'

PRINT ''

-- =====================================================
-- 4. OTTIMIZZAZIONE INDICI E SPAZIO
-- =====================================================

PRINT '4. OTTIMIZZAZIONE INDICI E SPAZIO'
PRINT '----------------------------------'

-- Riorganizza indici SOLO tabella Log (quella modificata)
PRINT 'Riorganizzazione indici tabella Log...'
ALTER INDEX ALL ON Log REORGANIZE
PRINT '✓ Indici tabella Log riorganizzati'

-- Aggiorna statistiche
PRINT 'Aggiornamento statistiche...'
UPDATE STATISTICS Log
PRINT '✓ Statistiche aggiornate'

PRINT ''

-- =====================================================
-- 5. RIDUZIONE FILE DATABASE
-- =====================================================

PRINT '5. RIDUZIONE FILE DATABASE'
PRINT '--------------------------'

-- Shrink del database
PRINT 'Riduzione dimensioni database...'
DBCC SHRINKDATABASE(iGrest, 10)
PRINT '✓ Database ridotto'

-- Shrink specifico del file di log
PRINT 'Riduzione file di log...'
DBCC SHRINKFILE('iGrest_log', 50)
PRINT '✓ File di log ridotto'

PRINT ''

-- =====================================================
-- 6. RISULTATI FINALI
-- =====================================================

PRINT '6. RISULTATI FINALI'
PRINT '-------------------'

-- Conteggio record finali
PRINT 'Record rimanenti:'
SELECT 'Logs (accessi utente)' as Tabella, COUNT(*) as Record FROM Logs
UNION ALL
SELECT 'Log (errori/debug)' as Tabella, COUNT(*) as Record FROM Log;

-- Dimensioni file finali
PRINT 'Dimensioni file finali:'
SELECT 
    name as NomeFile,
    type_desc as Tipo,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as UsatoMB,
    CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024 AS DECIMAL(15,2)) as LiberoMB
FROM sys.database_files;

PRINT ''

-- Verifica età rimanente dei log di errore
PRINT 'Verifica età rimanente dei log di errore:'
SELECT 
    MIN(DTINSERT) as LogErrorePiuVecchio,
    MAX(DTINSERT) as LogErrorePiuRecente,
    COUNT(*) as TotaleLogErrore
FROM Log 
WHERE DTINSERT IS NOT NULL;

PRINT ''
PRINT '=== OTTIMIZZAZIONE SELETTIVA COMPLETATA ==='
PRINT 'RIEPILOGO:'
PRINT '• Log accessi utente: MANTENUTI TUTTI (tabella Logs)'
PRINT '• Log errori/debug: eliminati quelli più vecchi di 90 giorni (tabella Log)'
PRINT '• File database ottimizzato'
PRINT ''
PRINT 'RACCOMANDAZIONI:'
PRINT '• Eseguire questa pulizia dei log di errore mensilmente'
PRINT '• Considerare l''implementazione di un job automatico'
PRINT '• I log degli accessi utente possono essere archiviati separatamente se necessario'
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)