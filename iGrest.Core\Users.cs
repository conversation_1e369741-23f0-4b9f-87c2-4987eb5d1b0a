//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Users
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Users()
        {
            this.Pagamenti = new HashSet<Pagamenti>();
            this.Rel_Users_News = new HashSet<Rel_Users_News>();
            this.Users_AdditionalDetails = new HashSet<Users_AdditionalDetails>();
            this.REL_Users_NotificPlayers = new HashSet<REL_Users_NotificPlayers>();
            this.Grest_Files_Users = new HashSet<Grest_Files_Users>();
            this.Rel_Users_Headquarter = new HashSet<Rel_Users_Headquarter>();
            this.News_Read = new HashSet<News_Read>();
            this.Order = new HashSet<Order>();
            this.Rel_Users_Trip_Bus = new HashSet<Rel_Users_Trip_Bus>();
            this.Rel_Users_Year = new HashSet<Rel_Users_Year>();
            this.Grest_Files = new HashSet<Grest_Files>();
            this.Bar = new HashSet<Bar>();
            this.InputOutput = new HashSet<InputOutput>();
            this.Listino = new HashSet<Listino>();
            this.Mensa = new HashSet<Mensa>();
            this.Rel_Users_Activity = new HashSet<Rel_Users_Activity>();
            this.Rel_Users_Groups = new HashSet<Rel_Users_Groups>();
            this.Users1 = new HashSet<Users>();
            this.Logs = new HashSet<Logs>();
            this.InputOutput1 = new HashSet<InputOutput>();
            this.Grest_Files1 = new HashSet<Grest_Files>();
            this.Rel_Users_TripsPrice = new HashSet<Rel_Users_TripsPrice>();
            this.Grest_Document = new HashSet<Grest_Document>();
            this.BalanceSheets = new HashSet<BalanceSheets>();
            this.BalanceSheets1 = new HashSet<BalanceSheets>();
            this.Rel_Users_Training = new HashSet<Rel_Users_Training>();
            this.Rel_User_SubscriptionAttachments = new HashSet<Rel_User_SubscriptionAttachments>();
            this.UserToken = new HashSet<UserToken>();
            this.Rel_Users_Trip = new HashSet<Rel_Users_Trip>();
            this.MessageRead = new HashSet<MessageRead>();
            this.Rel_Users = new HashSet<Rel_Users>();
            this.Rel_Users1 = new HashSet<Rel_Users>();
        }
    
        public System.Guid ID_User { get; set; }
        public Nullable<System.Guid> ID_Grest { get; set; }
        public Nullable<int> ID_Year { get; set; }
        public string Avatar { get; set; }
        public string Lastname { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Pwd { get; set; }
        public string Barcode { get; set; }
        public string BarcodeArchive { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Father { get; set; }
        public string FatherPhone { get; set; }
        public string Mother { get; set; }
        public string MotherPhone { get; set; }
        public string GrandparentsPhone { get; set; }
        public Nullable<System.DateTime> Birthday { get; set; }
        public string Gender { get; set; }
        public Nullable<int> ID_Provincia { get; set; }
        public Nullable<int> ID_Comune { get; set; }
        public string MedicalInfo { get; set; }
        public string MedicalCare { get; set; }
        public string SchoolName { get; set; }
        public string SchoolClass { get; set; }
        public string SchoolCity { get; set; }
        public string Size { get; set; }
        public string Note { get; set; }
        public Nullable<decimal> Conto { get; set; }
        public string Weeks { get; set; }
        public Nullable<System.Guid> ID_Parent { get; set; }
        public string InsuranceN { get; set; }
        public string ParentCf { get; set; }
        public string ChildCf { get; set; }
        public string Cap { get; set; }
        public string Citta { get; set; }
        public string Provincia { get; set; }
        public string CognomeGenitoreFiscale { get; set; }
        public string NomeGenitoreFiscale { get; set; }
        public string IndirizzoGenitoreFiscale { get; set; }
        public string CapGenitoreFiscale { get; set; }
        public string CittaGenitoreFiscale { get; set; }
        public string ProvinciaGenitoreFiscale { get; set; }
        public string CfGenitoreFiscale { get; set; }
        public string TempoPienoParziale { get; set; }
        public string NoteRiservate { get; set; }
        public Nullable<decimal> LimiteGiornalieroBar { get; set; }
        public string Country { get; set; }
        public string Extra1 { get; set; }
        public string Extra2 { get; set; }
        public string Extra3 { get; set; }
        public Nullable<System.DateTime> DataComunione { get; set; }
        public string LuogoComunione { get; set; }
        public Nullable<System.DateTime> DataBattesimo { get; set; }
        public string LuogoBattesimo { get; set; }
        public bool ResponsabilitaDati { get; set; }
        public string EmailSecondaria { get; set; }
        public int HeadquarterEnable { get; set; }
        public bool HeadquarterDefault { get; set; }
        public bool BattesimoAltraParrocchia { get; set; }
        public bool BattesimoFuoriCitta { get; set; }
        public bool BattesimoNullaOsta { get; set; }
        public Nullable<System.DateTime> DataCresima { get; set; }
        public string LuogoCresima { get; set; }
        public string CelebranteCresima { get; set; }
        public string NominativoPadrino { get; set; }
        public string NominativoMadrina { get; set; }
        public string IndirizzoPadrino { get; set; }
        public string IndirizzoMadrina { get; set; }
        public Nullable<System.DateTime> LastLoginDate { get; set; }
        public string TesseraSanitaria { get; set; }
        public string CartaIdentita { get; set; }
        public string CardN { get; set; }
        public bool BattesimoOrtodosso { get; set; }
        public string DescrGenitoreFiscale { get; set; }
        public int TripDiscount { get; set; }
        public byte TripDiscountType { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Pagamenti> Pagamenti { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_News> Rel_Users_News { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Users_AdditionalDetails> Users_AdditionalDetails { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<REL_Users_NotificPlayers> REL_Users_NotificPlayers { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files_Users> Grest_Files_Users { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Headquarter> Rel_Users_Headquarter { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<News_Read> News_Read { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Order> Order { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Trip_Bus> Rel_Users_Trip_Bus { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Year> Rel_Users_Year { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files> Grest_Files { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Bar> Bar { get; set; }
        public virtual Comuni Comuni { get; set; }
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<InputOutput> InputOutput { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Listino> Listino { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Mensa> Mensa { get; set; }
        public virtual Province Province { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Activity> Rel_Users_Activity { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Groups> Rel_Users_Groups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Users> Users1 { get; set; }
        public virtual Users Users2 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Logs> Logs { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<InputOutput> InputOutput1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files> Grest_Files1 { get; set; }
        public virtual SchoolClass SchoolClass1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Document> Grest_Document { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceSheets> BalanceSheets { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceSheets> BalanceSheets1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Training> Rel_Users_Training { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_User_SubscriptionAttachments> Rel_User_SubscriptionAttachments { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<UserToken> UserToken { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Trip> Rel_Users_Trip { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<MessageRead> MessageRead { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users> Rel_Users { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users> Rel_Users1 { get; set; }
    }
}
