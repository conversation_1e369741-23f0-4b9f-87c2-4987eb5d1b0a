GO

/****** Object:  View [dbo].[W_BalanceUsers]    Script Date: 2/9/2024 8:37:37 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




















ALTER VIEW [dbo].[W_BalanceUsers]
AS
SELECT        u.ID_User, ruy.ID_UserType, u.ID_Grest, u.ID_Year, ruy.DateInsert, u.Avatar, u.Lastname, u.Name, u.Email, u.Pwd, u.<PERSON>, u.BarcodeArchive, u.Address, u.<PERSON>, u<PERSON>, u<PERSON>, u<PERSON><PERSON>, u<PERSON>, 
                         u.<PERSON>, u.<PERSON>, u.<PERSON>, u.ID_Pro<PERSON>cia, u.ID_<PERSON>mune, cast(u.MedicalInfo AS NVARCHAR(MAX)) as MedicalInfo, cast(u.MedicalCare AS NVARCHAR(MAX)) as MedicalCare, u.SchoolName, u.SchoolClass, u.SchoolCity, u<PERSON>, cast(u.Note AS NVARCHAR(MAX)) as <PERSON>, ruy.Enabled as Enable, ruy.<PERSON><PERSON>, u<PERSON>, u<PERSON>, u.ID_Parent, 
                         u.<PERSON>, u.<PERSON>, u<PERSON>, u.Cap, u.<PERSON>itta, u.Provincia, u.CognomeGenitoreFiscale, u.NomeGenitoreFiscale, u.IndirizzoGenitoreFiscale, u.CapGenitoreFiscale, u.CittaGenitoreFiscale, u.ProvinciaGenitoreFiscale, 
                         u.CfGenitoreFiscale, u.TempoPienoParziale, u.NoteRiservate, u.LimiteGiornalieroBar, u.Country, u.Extra1, u.Extra2, u.Extra3, u.DataComunione, u.LuogoComunione, u.DataBattesimo, u.LuogoBattesimo, u.ResponsabilitaDati, 
                         u.EmailSecondaria, b.ID_Balance, b.ID_Grest AS Balance_ID_Grest, b.ID_UserType AS Balance_ID_UserType, b.ID_User AS Balance_ID_User, b.ID_Type, b.ID_Year AS Balance_ID_Year, b.DataMovimento, b.Importo, cast(b.Causale AS NVARCHAR(MAX)) as Causale, b.Removed AS Balance_Removed,
						 b.ID_Headquarter, b.Transaction_ID, b.Transaction_User_Firstname, b.Transaction_User_Lastname, b.ID_TransactionType, b.Transaction_User_Email, b.ID_PaymentType, b.Status, b.ID_UserCreated, b.ID_UserLastEdit, b.ID_IncomeStatement, b.IvaPerc, b.Hashtags
FROM            dbo.BalanceSheets AS b INNER JOIN
                         dbo.Users AS u ON u.ID_User = b.ID_User
						 inner join dbo.Rel_Users_Year ruy on ruy.ID_User = u.ID_User and ruy.id_year in (select max(ruy2.id_year) from Rel_Users_Year ruy2 inner join Grest_Year y2 on y2.ID_Year = ruy2.ID_Year and y2.Removed = 0 and ruy2.ID_User = u.ID_User)


UNION

SELECT        f.ID_Fornitore as ID_User, 0 as ID_UserType, f.ID_Grest, 0 as ID_Year, NULL as DateInsert, NULL as Avatar, NULL as Lastname, f.Name, NULL as Email, NULL as Pwd, NULL as Barcode, NULL as BarcodeArchive, f.Address, NULL as Phone, NULL as Father, NULL as FatherPhone, NULL as Mother, NULL as MotherPhone, 
                         NULL as GrandparentsPhone, NULL as Birthday, NULL as Gender, NULL as ID_Provincia, NULL as ID_Comune, NULL as MedicalInfo, NULL as MedicalCare, NULL as SchoolName, NULL as SchoolClass, NULL as SchoolCity, NULL as Size, NULL as Note, cast(1 as bit) as Enable, f.Removed, f.Conto, NULL as Weeks, NULL as ID_Parent, 
                         NULL as InsuranceN, NULL as ParentCf, isnull(f.Cf, f.Piva) as ChildCf, NULL as Cap, NULL as Citta, NULL as Provincia, NULL as CognomeGenitoreFiscale, NULL as NomeGenitoreFiscale, NULL as IndirizzoGenitoreFiscale, NULL as CapGenitoreFiscale, NULL as CittaGenitoreFiscale, NULL as ProvinciaGenitoreFiscale, 
                         NULL as CfGenitoreFiscale, NULL as TempoPienoParziale, NULL as NoteRiservate, NULL as LimiteGiornalieroBar, NULL as Country, NULL as Extra1, NULL as Extra2, NULL as Extra3, NULL as DataComunione, NULL as LuogoComunione, NULL as DataBattesimo, NULL as LuogoBattesimo, NULL as ResponsabilitaDati, 
                         NULL as EmailSecondaria, b.ID_Balance, b.ID_Grest AS Balance_ID_Grest, b.ID_UserType AS Balance_ID_UserType, b.ID_User AS Balance_ID_User, b.ID_Type, b.ID_Year AS Balance_ID_Year, b.DataMovimento, b.Importo, cast(b.Causale AS NVARCHAR(MAX)) as Causale, b.Removed AS Balance_Removed,
						 b.ID_Headquarter, b.Transaction_ID, b.Transaction_User_Firstname, b.Transaction_User_Lastname, b.ID_TransactionType, b.Transaction_User_Email, b.ID_PaymentType, b.Status, b.ID_UserCreated, b.ID_UserLastEdit, b.ID_IncomeStatement, b.IvaPerc, b.Hashtags
FROM            dbo.BalanceSheets AS b INNER JOIN
							dbo.Fornitori as f on f.ID_Fornitore = b.ID_User
GO


