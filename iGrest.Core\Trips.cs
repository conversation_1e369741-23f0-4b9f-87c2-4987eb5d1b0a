//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Trips
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Trips()
        {
            this.Rel_Users_Trip = new HashSet<Rel_Users_Trip>();
            this.Rel_Users_Trip_Bus = new HashSet<Rel_Users_Trip_Bus>();
            this.Rel_Users_TripsPrice = new HashSet<Rel_Users_TripsPrice>();
            this.Trip_Bus = new HashSet<Trip_Bus>();
            this.Groups = new HashSet<Groups>();
        }
    
        public System.Guid ID_Trip { get; set; }
        public System.Guid ID_Grest { get; set; }
        public Nullable<int> ID_Year { get; set; }
        public string Name { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public Nullable<decimal> UnitPrice { get; set; }
        public string Note { get; set; }
        public Nullable<bool> EntryOpen { get; set; }
        public Nullable<bool> Closed { get; set; }
        public Nullable<bool> Archived { get; set; }
        public Nullable<bool> Removed { get; set; }
        public Nullable<int> Capacity { get; set; }
        public Nullable<int> AgeFrom { get; set; }
        public Nullable<int> AgeTo { get; set; }
        public int ID_TripType { get; set; }
        public string PdfFile { get; set; }
        public string PdfFileText { get; set; }
        public Nullable<decimal> UnitPriceEducator { get; set; }
        public Nullable<System.DateTime> BirthdayFrom { get; set; }
        public Nullable<System.DateTime> BirthdayTo { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public Nullable<System.Guid> ID_TripMaster { get; set; }
        public bool HideCalendar { get; set; }
        public Nullable<System.DateTime> OpenRegStartDate { get; set; }
        public Nullable<System.DateTime> OpenRegEndDate { get; set; }
        public Nullable<int> CloseRegBeforeHours { get; set; }
        public bool PartecipantiCanView { get; set; }
        public bool EducatoriCanView { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Trip> Rel_Users_Trip { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Trip_Bus> Rel_Users_Trip_Bus { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trip_Bus> Trip_Bus { get; set; }
        public virtual TripsMaster TripsMaster { get; set; }
        public virtual TripType TripType { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Groups> Groups { get; set; }
    }
}
