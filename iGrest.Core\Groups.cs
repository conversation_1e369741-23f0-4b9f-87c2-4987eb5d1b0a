//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Groups
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Groups()
        {
            this.Rel_Users_Groups = new HashSet<Rel_Users_Groups>();
            this.Trainings = new HashSet<Trainings>();
            this.Trips1 = new HashSet<Trips>();
            this.Rel_Files_Groups = new HashSet<Rel_Files_Groups>();
        }
    
        public System.Guid ID_Group { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_Year { get; set; }
        public string Name { get; set; }
        public Nullable<int> AgeFrom { get; set; }
        public Nullable<int> AgeTo { get; set; }
        public string Gender { get; set; }
        public Nullable<bool> Removed { get; set; }
        public string Classe { get; set; }
        public string Description { get; set; }
        public Nullable<System.DateTime> BirthdayFrom { get; set; }
        public Nullable<System.DateTime> BirthdayTo { get; set; }
        public string Trips { get; set; }
        public bool EnableAnagrafica { get; set; }
        public bool EnableIscrizione { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Groups> Rel_Users_Groups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trainings> Trainings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trips> Trips1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Files_Groups> Rel_Files_Groups { get; set; }
    }
}
