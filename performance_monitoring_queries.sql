-- =====================================================
-- QUERY DI MONITORAGGIO POST-OTTIMIZZAZIONE iGREST
-- Data: 07/06/2025
-- Scopo: Verificare l'efficacia delle ottimizzazioni implementate
-- =====================================================

-- 1. ANALISI LOGIN ATTEMPTS (Verifica Rate Limiting)
-- Prima dell'ottimizzazione: 3,941 login in 24 ore (anomalo)
-- Obiettivo: Riduzione del 90% (~400 login/giorno)
SELECT 
    'LOGIN_ANALYSIS' as MetricType,
    CAST(DataInsert AS DATE) as Data,
    COUNT(*) as TotalLoginAttempts,
    COUNT(CASE WHEN Action LIKE '%failed%' OR Action LIKE '%blocked%' THEN 1 END) as FailedAttempts,
    COUNT(CASE WHEN Action LIKE '%rate%limit%' THEN 1 END) as RateLimitedAttempts,
    CAST(COUNT(CASE WHEN Action LIKE '%failed%' OR Action LIKE '%blocked%' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as FailureRate
FROM Log 
WHERE Action LIKE '%login%' 
    AND DataInsert >= DATEADD(day, -7, GETDATE())
GROUP BY CAST(DataInsert AS DATE)
ORDER BY Data DESC;

-- 2. ANALISI STRIPE ERRORS (Verifica Circuit Breaker)
-- Prima dell'ottimizzazione: 963 tentativi Stripe in 24 ore
-- Obiettivo: Riduzione del 95% (~50 tentativi/giorno)
SELECT 
    'STRIPE_ANALYSIS' as MetricType,
    CAST(DataInsert AS DATE) as Data,
    COUNT(*) as TotalStripeOperations,
    COUNT(CASE WHEN Action LIKE '%error%' OR Action LIKE '%exception%' THEN 1 END) as StripeErrors,
    COUNT(CASE WHEN Action LIKE '%circuit%breaker%' THEN 1 END) as CircuitBreakerActivations,
    COUNT(CASE WHEN Action LIKE '%retry%' THEN 1 END) as RetryAttempts,
    CAST(COUNT(CASE WHEN Action LIKE '%error%' OR Action LIKE '%exception%' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as ErrorRate
FROM Log 
WHERE Action LIKE '%stripe%'
    AND DataInsert >= DATEADD(day, -7, GETDATE())
GROUP BY CAST(DataInsert AS DATE)
ORDER BY Data DESC;

-- 3. ANALISI PERFORMANCE DATABASE (Verifica Batch Logging)
-- Prima dell'ottimizzazione: 61,288 CPU units SQL Server
-- Obiettivo: Riduzione del 60-70% del carico CPU
SELECT 
    'DATABASE_PERFORMANCE' as MetricType,
    DATEPART(hour, DataInsert) as Ora,
    COUNT(*) as DatabaseOperations,
    COUNT(CASE WHEN Action LIKE '%batch%' THEN 1 END) as BatchOperations,
    COUNT(CASE WHEN Action LIKE '%slow%' OR Action LIKE '%timeout%' THEN 1 END) as SlowOperations,
    AVG(CASE WHEN Action LIKE '%duration%' THEN 
        TRY_CAST(SUBSTRING(Action, CHARINDEX('duration:', Action) + 9, 10) AS INT) 
        ELSE NULL END) as AvgDurationMs
FROM Log 
WHERE (Action LIKE '%database%' OR Action LIKE '%sql%' OR Action LIKE '%query%')
    AND DataInsert >= DATEADD(day, -1, GETDATE())
GROUP BY DATEPART(hour, DataInsert)
ORDER BY Ora;

-- 4. ANALISI VOLUME LOG TOTALE (Verifica Cleanup Automatico)
-- Obiettivo: Riduzione dell'80% del volume di log
SELECT 
    'LOG_VOLUME_ANALYSIS' as MetricType,
    CAST(DataInsert AS DATE) as Data,
    COUNT(*) as TotalLogs,
    COUNT(CASE WHEN Action LIKE '%error%' OR Action LIKE '%exception%' THEN 1 END) as ErrorLogs,
    COUNT(CASE WHEN Action LIKE '%cleanup%' THEN 1 END) as CleanupOperations,
    CAST(COUNT(CASE WHEN Action LIKE '%error%' OR Action LIKE '%exception%' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,2)) as ErrorLogPercentage
FROM Log 
WHERE DataInsert >= DATEADD(day, -7, GETDATE())
GROUP BY CAST(DataInsert AS DATE)
ORDER BY Data DESC;

-- 5. CONFRONTO PRE/POST OTTIMIZZAZIONE (Ultime 48 ore)
SELECT 
    'COMPARISON_24H' as MetricType,
    CASE 
        WHEN DataInsert >= DATEADD(hour, -24, GETDATE()) THEN 'LAST_24H_POST_OPT'
        ELSE 'PREVIOUS_24H_PRE_OPT'
    END as Period,
    COUNT(*) as TotalLogs,
    COUNT(CASE WHEN Action LIKE '%login%' THEN 1 END) as LoginAttempts,
    COUNT(CASE WHEN Action LIKE '%stripe%error%' THEN 1 END) as StripeErrors,
    COUNT(CASE WHEN Action LIKE '%database%' THEN 1 END) as DatabaseOps,
    COUNT(CASE WHEN Action LIKE '%error%' OR Action LIKE '%exception%' THEN 1 END) as TotalErrors
FROM Log 
WHERE DataInsert >= DATEADD(hour, -48, GETDATE())
GROUP BY CASE 
    WHEN DataInsert >= DATEADD(hour, -24, GETDATE()) THEN 'LAST_24H_POST_OPT'
    ELSE 'PREVIOUS_24H_PRE_OPT'
END;

-- 6. TOP 10 ERRORI PIÙ FREQUENTI (Ultimi 7 giorni)
SELECT TOP 10
    'TOP_ERRORS' as MetricType,
    Action,
    COUNT(*) as ErrorCount,
    MAX(DataInsert) as LastOccurrence,
    MIN(DataInsert) as FirstOccurrence,
    COUNT(DISTINCT CAST(DataInsert AS DATE)) as DaysWithError
FROM Log 
WHERE (Action LIKE '%error%' OR Action LIKE '%exception%' OR Action LIKE '%fail%')
    AND DataInsert >= DATEADD(day, -7, GETDATE())
GROUP BY Action
ORDER BY COUNT(*) DESC;

-- 7. ANALISI EFFICACIA RATE LIMITING PER IP
SELECT 
    'RATE_LIMITING_BY_IP' as MetricType,
    SUBSTRING(Action, CHARINDEX('IP:', Action) + 3, CHARINDEX(' ', Action + ' ', CHARINDEX('IP:', Action)) - CHARINDEX('IP:', Action) - 3) as ClientIP,
    COUNT(*) as TotalRequests,
    COUNT(CASE WHEN Action LIKE '%blocked%' OR Action LIKE '%rate%limit%' THEN 1 END) as BlockedRequests,
    MAX(DataInsert) as LastActivity
FROM Log 
WHERE Action LIKE '%IP:%'
    AND DataInsert >= DATEADD(day, -1, GETDATE())
GROUP BY SUBSTRING(Action, CHARINDEX('IP:', Action) + 3, CHARINDEX(' ', Action + ' ', CHARINDEX('IP:', Action)) - CHARINDEX('IP:', Action) - 3)
HAVING COUNT(*) > 10  -- Solo IP con più di 10 richieste
ORDER BY COUNT(*) DESC;

-- 8. VERIFICA FUNZIONAMENTO CIRCUIT BREAKER
SELECT 
    'CIRCUIT_BREAKER_STATUS' as MetricType,
    CAST(DataInsert AS DATE) as Data,
    COUNT(CASE WHEN Action LIKE '%circuit%breaker%open%' THEN 1 END) as CircuitBreakerOpened,
    COUNT(CASE WHEN Action LIKE '%circuit%breaker%closed%' THEN 1 END) as CircuitBreakerClosed,
    COUNT(CASE WHEN Action LIKE '%retry%blocked%' THEN 1 END) as RetriesBlocked,
    MAX(CASE WHEN Action LIKE '%circuit%breaker%' THEN DataInsert END) as LastCircuitBreakerActivity
FROM Log 
WHERE Action LIKE '%circuit%breaker%' OR Action LIKE '%retry%blocked%'
    AND DataInsert >= DATEADD(day, -7, GETDATE())
GROUP BY CAST(DataInsert AS DATE)
ORDER BY Data DESC;

-- 9. PERFORMANCE RESPONSE TIME ANALYSIS
SELECT 
    'RESPONSE_TIME_ANALYSIS' as MetricType,
    DATEPART(hour, DataInsert) as Ora,
    COUNT(*) as TotalRequests,
    AVG(CASE WHEN Action LIKE '%response_time:%' THEN 
        TRY_CAST(SUBSTRING(Action, CHARINDEX('response_time:', Action) + 14, 10) AS INT) 
        ELSE NULL END) as AvgResponseTimeMs,
    COUNT(CASE WHEN Action LIKE '%slow%response%' THEN 1 END) as SlowResponses,
    COUNT(CASE WHEN Action LIKE '%timeout%' THEN 1 END) as Timeouts
FROM Log 
WHERE DataInsert >= DATEADD(day, -1, GETDATE())
GROUP BY DATEPART(hour, DataInsert)
ORDER BY Ora;

-- 10. SUMMARY REPORT - EFFICACIA OTTIMIZZAZIONI
SELECT 
    'OPTIMIZATION_SUMMARY' as MetricType,
    'Current Status' as Period,
    
    -- Login Rate Limiting
    (SELECT COUNT(*) FROM Log WHERE Action LIKE '%login%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) as LoginAttempts24h,
    CASE 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%login%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 500 
        THEN 'OTTIMO (< 500/giorno)' 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%login%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 1000 
        THEN 'BUONO (< 1000/giorno)'
        ELSE 'DA MIGLIORARE (> 1000/giorno)'
    END as LoginRateLimitingStatus,
    
    -- Stripe Circuit Breaker
    (SELECT COUNT(*) FROM Log WHERE Action LIKE '%stripe%error%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) as StripeErrors24h,
    CASE 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%stripe%error%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 100 
        THEN 'OTTIMO (< 100/giorno)' 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%stripe%error%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 300 
        THEN 'BUONO (< 300/giorno)'
        ELSE 'DA MIGLIORARE (> 300/giorno)'
    END as StripeCircuitBreakerStatus,
    
    -- Database Performance
    (SELECT COUNT(*) FROM Log WHERE Action LIKE '%database%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) as DatabaseOps24h,
    CASE 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%slow%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 50 
        THEN 'OTTIMO (< 50 operazioni lente/giorno)' 
        WHEN (SELECT COUNT(*) FROM Log WHERE Action LIKE '%slow%' AND DataInsert >= DATEADD(hour, -24, GETDATE())) < 200 
        THEN 'BUONO (< 200 operazioni lente/giorno)'
        ELSE 'DA MIGLIORARE (> 200 operazioni lente/giorno)'
    END as DatabasePerformanceStatus,
    
    -- Log Volume
    (SELECT COUNT(*) FROM Log WHERE DataInsert >= DATEADD(hour, -24, GETDATE())) as TotalLogs24h,
    CASE 
        WHEN (SELECT COUNT(*) FROM Log WHERE DataInsert >= DATEADD(hour, -24, GETDATE())) < 5000 
        THEN 'OTTIMO (< 5000 log/giorno)' 
        WHEN (SELECT COUNT(*) FROM Log WHERE DataInsert >= DATEADD(hour, -24, GETDATE())) < 15000 
        THEN 'BUONO (< 15000 log/giorno)'
        ELSE 'DA MIGLIORARE (> 15000 log/giorno)'
    END as LogVolumeStatus;

-- =====================================================
-- ISTRUZIONI PER L'ESECUZIONE:
-- 1. Eseguire queste query sul database iGrest
-- 2. Confrontare i risultati con i valori pre-ottimizzazione:
--    - Login: da 3,941 a ~400/giorno (-90%)
--    - Stripe: da 963 a ~50/giorno (-95%)
--    - CPU: da 61,288 units a ~20,000 units (-67%)
--    - Log Volume: riduzione dell'80%
-- 3. Monitorare quotidianamente per 1 settimana
-- 4. Regolare i parametri se necessario
-- =====================================================