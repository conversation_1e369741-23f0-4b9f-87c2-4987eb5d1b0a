//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class TripsMaster
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public TripsMaster()
        {
            this.Trips = new HashSet<Trips>();
            this.TripsMaster1 = new HashSet<TripsMaster>();
            this.TripsPrice = new HashSet<TripsPrice>();
            this.TripsDiscountConditions = new HashSet<TripsDiscountConditions>();
        }
    
        public System.Guid ID_TripMaster { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_Year { get; set; }
        public string Name { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public Nullable<decimal> UnitPrice { get; set; }
        public string Note { get; set; }
        public Nullable<bool> EntryOpen { get; set; }
        public Nullable<bool> Closed { get; set; }
        public Nullable<bool> Archived { get; set; }
        public Nullable<bool> Removed { get; set; }
        public Nullable<int> Capacity { get; set; }
        public Nullable<int> AgeFrom { get; set; }
        public Nullable<int> AgeTo { get; set; }
        public int ID_TripType { get; set; }
        public Nullable<decimal> UnitPriceEducator { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public string PdfFile { get; set; }
        public string PdfFileText { get; set; }
        public bool Recurring { get; set; }
        public Nullable<System.DateTime> BirthdayFrom { get; set; }
        public Nullable<System.DateTime> BirthdayTo { get; set; }
        public string Hashtags { get; set; }
        public bool HideCalendar { get; set; }
        public Nullable<System.DateTime> OpenRegStartDate { get; set; }
        public Nullable<System.DateTime> OpenRegEndDate { get; set; }
        public Nullable<int> CloseRegBeforeHours { get; set; }
        public bool PartecipantiCanView { get; set; }
        public bool EducatoriCanView { get; set; }
        public Nullable<System.Guid> ID_IncomeStatement { get; set; }
        public bool UnsubscribeEnabled { get; set; }
        public bool ApplyDiscounts { get; set; }
        public Nullable<System.Guid> MandatorySubscriptionTripMasterID { get; set; }
        public bool RecurringAdvanced { get; set; }
        public bool PaymentOption { get; set; }
        public bool AllowNote { get; set; }
    
        public virtual BalanceIncomeStatements BalanceIncomeStatements { get; set; }
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trips> Trips { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsMaster> TripsMaster1 { get; set; }
        public virtual TripsMaster TripsMaster2 { get; set; }
        public virtual TripType TripType { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsPrice> TripsPrice { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsDiscountConditions> TripsDiscountConditions { get; set; }
    }
}
