//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class News
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public News()
        {
            this.Rel_Users_News = new HashSet<Rel_Users_News>();
            this.News_Read = new HashSet<News_Read>();
        }
    
        public System.Guid ID_News { get; set; }
        public System.Guid ID_Grest { get; set; }
        public Nullable<System.DateTime> DatePage { get; set; }
        public string Title { get; set; }
        public string Image { get; set; }
        public string Intro { get; set; }
        public string Text { get; set; }
        public Nullable<System.DateTime> LastUpdate { get; set; }
        public Nullable<System.Guid> UserUpdate { get; set; }
        public bool Enable { get; set; }
        public bool Removed { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_News> Rel_Users_News { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<News_Read> News_Read { get; set; }
        public virtual Grest Grest { get; set; }
    }
}
