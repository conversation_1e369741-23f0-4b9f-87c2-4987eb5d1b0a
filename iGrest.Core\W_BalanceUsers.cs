//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class W_BalanceUsers
    {
        public System.Guid ID_User { get; set; }
        public int ID_UserType { get; set; }
        public Nullable<System.Guid> ID_Grest { get; set; }
        public Nullable<int> ID_Year { get; set; }
        public Nullable<System.DateTime> DateInsert { get; set; }
        public string Avatar { get; set; }
        public string Lastname { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string Pwd { get; set; }
        public string Barcode { get; set; }
        public string BarcodeArchive { get; set; }
        public string Address { get; set; }
        public string Phone { get; set; }
        public string Father { get; set; }
        public string FatherPhone { get; set; }
        public string Mother { get; set; }
        public string MotherPhone { get; set; }
        public string GrandparentsPhone { get; set; }
        public Nullable<System.DateTime> Birthday { get; set; }
        public string Gender { get; set; }
        public Nullable<int> ID_Provincia { get; set; }
        public Nullable<int> ID_Comune { get; set; }
        public string MedicalInfo { get; set; }
        public string MedicalCare { get; set; }
        public string SchoolName { get; set; }
        public string SchoolClass { get; set; }
        public string SchoolCity { get; set; }
        public string Size { get; set; }
        public string Note { get; set; }
        public Nullable<bool> Enable { get; set; }
        public Nullable<bool> Removed { get; set; }
        public Nullable<decimal> Conto { get; set; }
        public string Weeks { get; set; }
        public Nullable<System.Guid> ID_Parent { get; set; }
        public string InsuranceN { get; set; }
        public string ParentCf { get; set; }
        public string ChildCf { get; set; }
        public string Cap { get; set; }
        public string Citta { get; set; }
        public string Provincia { get; set; }
        public string CognomeGenitoreFiscale { get; set; }
        public string NomeGenitoreFiscale { get; set; }
        public string IndirizzoGenitoreFiscale { get; set; }
        public string CapGenitoreFiscale { get; set; }
        public string CittaGenitoreFiscale { get; set; }
        public string ProvinciaGenitoreFiscale { get; set; }
        public string CfGenitoreFiscale { get; set; }
        public string TempoPienoParziale { get; set; }
        public string NoteRiservate { get; set; }
        public Nullable<decimal> LimiteGiornalieroBar { get; set; }
        public string Country { get; set; }
        public string Extra1 { get; set; }
        public string Extra2 { get; set; }
        public string Extra3 { get; set; }
        public Nullable<System.DateTime> DataComunione { get; set; }
        public string LuogoComunione { get; set; }
        public Nullable<System.DateTime> DataBattesimo { get; set; }
        public string LuogoBattesimo { get; set; }
        public Nullable<bool> ResponsabilitaDati { get; set; }
        public string EmailSecondaria { get; set; }
        public System.Guid ID_Balance { get; set; }
        public System.Guid Balance_ID_Grest { get; set; }
        public int Balance_ID_UserType { get; set; }
        public System.Guid Balance_ID_User { get; set; }
        public int ID_Type { get; set; }
        public int Balance_ID_Year { get; set; }
        public Nullable<System.DateTime> DataMovimento { get; set; }
        public Nullable<decimal> Importo { get; set; }
        public string Causale { get; set; }
        public bool Balance_Removed { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public string Transaction_ID { get; set; }
        public string Transaction_User_Firstname { get; set; }
        public string Transaction_User_Lastname { get; set; }
        public int ID_TransactionType { get; set; }
        public string Transaction_User_Email { get; set; }
        public int ID_PaymentType { get; set; }
        public byte Status { get; set; }
        public Nullable<System.Guid> ID_UserCreated { get; set; }
        public Nullable<System.Guid> ID_UserLastEdit { get; set; }
        public Nullable<System.Guid> ID_IncomeStatement { get; set; }
        public decimal IvaPerc { get; set; }
        public string Hashtags { get; set; }
    }
}
