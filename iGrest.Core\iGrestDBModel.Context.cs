﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    using System.Data.Entity.Core.Objects;
    using System.Linq;
    
    public partial class iGrestEntities : DbContext
    {
        public iGrestEntities()
            : base("name=iGrestEntities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<Activity> Activity { get; set; }
        public virtual DbSet<Bar> Bar { get; set; }
        public virtual DbSet<Comuni> Comuni { get; set; }
        public virtual DbSet<Dinners> Dinners { get; set; }
        public virtual DbSet<Fornitori> Fornitori { get; set; }
        public virtual DbSet<Grest> Grest { get; set; }
        public virtual DbSet<Grest_Files> Grest_Files { get; set; }
        public virtual DbSet<Grest_FilesType> Grest_FilesType { get; set; }
        public virtual DbSet<Grest_Iscrizione_Type> Grest_Iscrizione_Type { get; set; }
        public virtual DbSet<Grest_Progressivo_Ricevuta> Grest_Progressivo_Ricevuta { get; set; }
        public virtual DbSet<Grest_UserAdditionalDetails> Grest_UserAdditionalDetails { get; set; }
        public virtual DbSet<Grest_Year> Grest_Year { get; set; }
        public virtual DbSet<GrestFatt> GrestFatt { get; set; }
        public virtual DbSet<Groups> Groups { get; set; }
        public virtual DbSet<InputOutput> InputOutput { get; set; }
        public virtual DbSet<Listino> Listino { get; set; }
        public virtual DbSet<Log> Log { get; set; }
        public virtual DbSet<Logs> Logs { get; set; }
        public virtual DbSet<Mensa> Mensa { get; set; }
        public virtual DbSet<News> News { get; set; }
        public virtual DbSet<Pagamenti> Pagamenti { get; set; }
        public virtual DbSet<PayPalConfig> PayPalConfig { get; set; }
        public virtual DbSet<Prodotti> Prodotti { get; set; }
        public virtual DbSet<Province> Province { get; set; }
        public virtual DbSet<Regioni> Regioni { get; set; }
        public virtual DbSet<Rel_Users_Activity> Rel_Users_Activity { get; set; }
        public virtual DbSet<Rel_Users_Groups> Rel_Users_Groups { get; set; }
        public virtual DbSet<Rel_Users_News> Rel_Users_News { get; set; }
        public virtual DbSet<Rel_Users_Trip_Bus> Rel_Users_Trip_Bus { get; set; }
        public virtual DbSet<Trip_Bus> Trip_Bus { get; set; }
        public virtual DbSet<TripType> TripType { get; set; }
        public virtual DbSet<Users> Users { get; set; }
        public virtual DbSet<Users_AdditionalDetails> Users_AdditionalDetails { get; set; }
        public virtual DbSet<UserType> UserType { get; set; }
        public virtual DbSet<NotificType> NotificType { get; set; }
        public virtual DbSet<REL_Users_NotificPlayers> REL_Users_NotificPlayers { get; set; }
        public virtual DbSet<Grest_Files_Users> Grest_Files_Users { get; set; }
        public virtual DbSet<GrestHeadquarters> GrestHeadquarters { get; set; }
        public virtual DbSet<Rel_Users_Headquarter> Rel_Users_Headquarter { get; set; }
        public virtual DbSet<TransactionType> TransactionType { get; set; }
        public virtual DbSet<News_Read> News_Read { get; set; }
        public virtual DbSet<Order> Order { get; set; }
        public virtual DbSet<OrderItem> OrderItem { get; set; }
        public virtual DbSet<Triage> Triage { get; set; }
        public virtual DbSet<Triage_Categorie> Triage_Categorie { get; set; }
        public virtual DbSet<Triage_InputOutput> Triage_InputOutput { get; set; }
        public virtual DbSet<PaymentType> PaymentType { get; set; }
        public virtual DbSet<ProdottiTipo> ProdottiTipo { get; set; }
        public virtual DbSet<Rel_Users_Year> Rel_Users_Year { get; set; }
        public virtual DbSet<Portale> Portale { get; set; }
        public virtual DbSet<StripeConfig> StripeConfig { get; set; }
        public virtual DbSet<StripeTransaction> StripeTransaction { get; set; }
        public virtual DbSet<FE_CondizioniPagamento> FE_CondizioniPagamento { get; set; }
        public virtual DbSet<FE_FormatoTrasmissione> FE_FormatoTrasmissione { get; set; }
        public virtual DbSet<FE_ModalitaPagamento> FE_ModalitaPagamento { get; set; }
        public virtual DbSet<FE_RegimeFiscale> FE_RegimeFiscale { get; set; }
        public virtual DbSet<FE_TipoDocumento> FE_TipoDocumento { get; set; }
        public virtual DbSet<BalanceIncomeStatements> BalanceIncomeStatements { get; set; }
        public virtual DbSet<SchoolClass> SchoolClass { get; set; }
        public virtual DbSet<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        public virtual DbSet<TripsPrice> TripsPrice { get; set; }
        public virtual DbSet<SatispayConfig> SatispayConfig { get; set; }
        public virtual DbSet<SatispayTransaction> SatispayTransaction { get; set; }
        public virtual DbSet<Grest_Document> Grest_Document { get; set; }
        public virtual DbSet<BalanceSheets> BalanceSheets { get; set; }
        public virtual DbSet<W_BalanceUsers> W_BalanceUsers { get; set; }
        public virtual DbSet<BalanceReceiptDetails> BalanceReceiptDetails { get; set; }
        public virtual DbSet<Rel_Users_Training> Rel_Users_Training { get; set; }
        public virtual DbSet<Trainings> Trainings { get; set; }
        public virtual DbSet<Rel_User_SubscriptionAttachments> Rel_User_SubscriptionAttachments { get; set; }
        public virtual DbSet<SubscriptionAttachments> SubscriptionAttachments { get; set; }
        public virtual DbSet<Sizes> Sizes { get; set; }
        public virtual DbSet<ProdottiCategory> ProdottiCategory { get; set; }
        public virtual DbSet<PrintableObject> PrintableObject { get; set; }
        public virtual DbSet<BankTransferConfig> BankTransferConfig { get; set; }
        public virtual DbSet<GrestMenu> GrestMenu { get; set; }
        public virtual DbSet<GrestMenuCustom> GrestMenuCustom { get; set; }
        public virtual DbSet<Configurations> Configurations { get; set; }
        public virtual DbSet<Temp_Table> Temp_Table { get; set; }
        public virtual DbSet<UserToken> UserToken { get; set; }
        public virtual DbSet<Rel_Users_Trip> Rel_Users_Trip { get; set; }
        public virtual DbSet<Trips> Trips { get; set; }
        public virtual DbSet<TripsMaster> TripsMaster { get; set; }
        public virtual DbSet<Rel_Files_Groups> Rel_Files_Groups { get; set; }
        public virtual DbSet<Rel_Files_Fornitori> Rel_Files_Fornitori { get; set; }
        public virtual DbSet<MessageRead> MessageRead { get; set; }
        public virtual DbSet<Messages> Messages { get; set; }
        public virtual DbSet<Rel_Users> Rel_Users { get; set; }
        public virtual DbSet<Rel_UsersType> Rel_UsersType { get; set; }
        public virtual DbSet<TripsDiscountConditions> TripsDiscountConditions { get; set; }
    
        [DbFunction("iGrestEntities", "Split")]
        public virtual IQueryable<Split_Result> Split(string @string, string delimiter)
        {
            var stringParameter = @string != null ?
                new ObjectParameter("String", @string) :
                new ObjectParameter("String", typeof(string));
    
            var delimiterParameter = delimiter != null ?
                new ObjectParameter("Delimiter", delimiter) :
                new ObjectParameter("Delimiter", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.CreateQuery<Split_Result>("[iGrestEntities].[Split](@String, @Delimiter)", stringParameter, delimiterParameter);
        }
    
        public virtual int spActivityGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spActivityGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spActivityGetsPA(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Activity)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_ActivityParameter = iD_Activity.HasValue ?
                new ObjectParameter("ID_Activity", iD_Activity) :
                new ObjectParameter("ID_Activity", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spActivityGetsPA", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_UserTypeParameter, iD_ActivityParameter);
        }
    
        public virtual int spActivityGetsTotal(string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spActivityGetsTotal", sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spActivityGetsTotalPA(string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Activity)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_ActivityParameter = iD_Activity.HasValue ?
                new ObjectParameter("ID_Activity", iD_Activity) :
                new ObjectParameter("ID_Activity", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spActivityGetsTotalPA", sSearchParameter, iD_UserTypeParameter, iD_ActivityParameter);
        }
    
        public virtual int spActivityGetsUser(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spActivityGetsUser", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter, iD_UserParameter);
        }
    
        public virtual int spBalanceGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Year, Nullable<int> iD_UserType, Nullable<System.Guid> iD_User, Nullable<int> iD_Type, Nullable<System.DateTime> dateFrom, Nullable<System.DateTime> dateTo)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var iD_TypeParameter = iD_Type.HasValue ?
                new ObjectParameter("ID_Type", iD_Type) :
                new ObjectParameter("ID_Type", typeof(int));
    
            var dateFromParameter = dateFrom.HasValue ?
                new ObjectParameter("DateFrom", dateFrom) :
                new ObjectParameter("DateFrom", typeof(System.DateTime));
    
            var dateToParameter = dateTo.HasValue ?
                new ObjectParameter("DateTo", dateTo) :
                new ObjectParameter("DateTo", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spBalanceGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter, iD_YearParameter, iD_UserTypeParameter, iD_UserParameter, iD_TypeParameter, dateFromParameter, dateToParameter);
        }
    
        public virtual int spBalanceGetsTotal(string sSearch, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Year, Nullable<int> iD_UserType, Nullable<System.Guid> iD_User, Nullable<int> iD_Type, Nullable<System.DateTime> dateFrom, Nullable<System.DateTime> dateTo)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var iD_TypeParameter = iD_Type.HasValue ?
                new ObjectParameter("ID_Type", iD_Type) :
                new ObjectParameter("ID_Type", typeof(int));
    
            var dateFromParameter = dateFrom.HasValue ?
                new ObjectParameter("DateFrom", dateFrom) :
                new ObjectParameter("DateFrom", typeof(System.DateTime));
    
            var dateToParameter = dateTo.HasValue ?
                new ObjectParameter("DateTo", dateTo) :
                new ObjectParameter("DateTo", typeof(System.DateTime));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spBalanceGetsTotal", sSearchParameter, iD_GrestParameter, iD_YearParameter, iD_UserTypeParameter, iD_UserParameter, iD_TypeParameter, dateFromParameter, dateToParameter);
        }
    
        public virtual ObjectResult<Nullable<decimal>> spBarGetDailyBalance(Nullable<System.Guid> iD_User)
        {
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<decimal>>("spBarGetDailyBalance", iD_UserParameter);
        }
    
        public virtual int spBarGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Prodotto, Nullable<int> userType, Nullable<System.Guid> userId)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_ProdottoParameter = iD_Prodotto.HasValue ?
                new ObjectParameter("ID_Prodotto", iD_Prodotto) :
                new ObjectParameter("ID_Prodotto", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spBarGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_ProdottoParameter, userTypeParameter, userIdParameter);
        }
    
        public virtual int spBarGetsTotal(string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Prodotto, Nullable<int> userType)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_ProdottoParameter = iD_Prodotto.HasValue ?
                new ObjectParameter("ID_Prodotto", iD_Prodotto) :
                new ObjectParameter("ID_Prodotto", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spBarGetsTotal", sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_ProdottoParameter, userTypeParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> spBarMaxIdOrdine(Nullable<System.Guid> iD_Grest, Nullable<int> iD_Year)
        {
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("spBarMaxIdOrdine", iD_GrestParameter, iD_YearParameter);
        }
    
        public virtual ObjectResult<Nullable<System.Guid>> spCheckBarcode(string barcode, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Year)
        {
            var barcodeParameter = barcode != null ?
                new ObjectParameter("Barcode", barcode) :
                new ObjectParameter("Barcode", typeof(string));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<System.Guid>>("spCheckBarcode", barcodeParameter, iD_GrestParameter, iD_YearParameter);
        }
    
        public virtual ObjectResult<Nullable<System.Guid>> spCheckUserIsInMensa(Nullable<System.DateTime> data, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User, Nullable<int> iD_Year)
        {
            var dataParameter = data.HasValue ?
                new ObjectParameter("Data", data) :
                new ObjectParameter("Data", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<System.Guid>>("spCheckUserIsInMensa", dataParameter, iD_GrestParameter, iD_UserParameter, iD_YearParameter);
        }
    
        public virtual ObjectResult<Nullable<System.Guid>> spCheckUserIsInOut(Nullable<System.DateTime> data, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User, Nullable<int> iD_Year, string type)
        {
            var dataParameter = data.HasValue ?
                new ObjectParameter("Data", data) :
                new ObjectParameter("Data", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var typeParameter = type != null ?
                new ObjectParameter("Type", type) :
                new ObjectParameter("Type", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<System.Guid>>("spCheckUserIsInOut", dataParameter, iD_GrestParameter, iD_UserParameter, iD_YearParameter, typeParameter);
        }
    
        public virtual int spFornitoreGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spFornitoreGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spFornitoreGetsTotal(string sSearch, string iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spFornitoreGetsTotal", sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spGrestFileGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest, string iD_User, Nullable<int> iD_FileType, string sharedWith, Nullable<bool> excludeAll, Nullable<bool> excludeExp)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var iD_UserParameter = iD_User != null ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(string));
    
            var iD_FileTypeParameter = iD_FileType.HasValue ?
                new ObjectParameter("ID_FileType", iD_FileType) :
                new ObjectParameter("ID_FileType", typeof(int));
    
            var sharedWithParameter = sharedWith != null ?
                new ObjectParameter("SharedWith", sharedWith) :
                new ObjectParameter("SharedWith", typeof(string));
    
            var excludeAllParameter = excludeAll.HasValue ?
                new ObjectParameter("ExcludeAll", excludeAll) :
                new ObjectParameter("ExcludeAll", typeof(bool));
    
            var excludeExpParameter = excludeExp.HasValue ?
                new ObjectParameter("ExcludeExp", excludeExp) :
                new ObjectParameter("ExcludeExp", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestFileGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter, iD_UserParameter, iD_FileTypeParameter, sharedWithParameter, excludeAllParameter, excludeExpParameter);
        }
    
        public virtual int spGrestFileTotal(string sSearch, string iD_Grest, string iD_User, string sharedWith, Nullable<int> iD_FileType, Nullable<bool> excludeAll, Nullable<bool> excludeExp)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var iD_UserParameter = iD_User != null ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(string));
    
            var sharedWithParameter = sharedWith != null ?
                new ObjectParameter("SharedWith", sharedWith) :
                new ObjectParameter("SharedWith", typeof(string));
    
            var iD_FileTypeParameter = iD_FileType.HasValue ?
                new ObjectParameter("ID_FileType", iD_FileType) :
                new ObjectParameter("ID_FileType", typeof(int));
    
            var excludeAllParameter = excludeAll.HasValue ?
                new ObjectParameter("ExcludeAll", excludeAll) :
                new ObjectParameter("ExcludeAll", typeof(bool));
    
            var excludeExpParameter = excludeExp.HasValue ?
                new ObjectParameter("ExcludeExp", excludeExp) :
                new ObjectParameter("ExcludeExp", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestFileTotal", sSearchParameter, iD_GrestParameter, iD_UserParameter, sharedWithParameter, iD_FileTypeParameter, excludeAllParameter, excludeExpParameter);
        }
    
        public virtual int spGrestGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter);
        }
    
        public virtual int spGrestGetsTotal(string sSearch)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestGetsTotal", sSearchParameter);
        }
    
        public virtual int spGrestIscrizioneTypeGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestIscrizioneTypeGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spGrestIscrizioneTypeTotal(string sSearch, string iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestIscrizioneTypeTotal", sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spGrestYearGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestYearGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spGrestYearTotal(string sSearch, string iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGrestYearTotal", sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spGroupGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spGroupGetsGroupUser(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGetsGroupUser", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter, iD_UserParameter);
        }
    
        public virtual int spGroupGetsPA(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Group)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_GroupParameter = iD_Group.HasValue ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGetsPA", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_UserTypeParameter, iD_GroupParameter);
        }
    
        public virtual int spGroupGetsTotal(string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGetsTotal", sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spGroupGetsTotalPA(string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Group)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_GroupParameter = iD_Group.HasValue ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGetsTotalPA", sSearchParameter, iD_UserTypeParameter, iD_GroupParameter);
        }
    
        public virtual int spGroupGetsUser(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spGroupGetsUser", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter, iD_UserParameter);
        }
    
        public virtual int spInputOutputGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.DateTime> dateStart, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, string action, Nullable<System.Guid> iD_User, Nullable<int> birthdayStart, Nullable<int> birthdayEnd, string classe, Nullable<int> iD_Dinner, Nullable<System.Guid> iD_Group, string sesso, string weeks, Nullable<int> ruolo)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateStartParameter = dateStart.HasValue ?
                new ObjectParameter("DateStart", dateStart) :
                new ObjectParameter("DateStart", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var actionParameter = action != null ?
                new ObjectParameter("Action", action) :
                new ObjectParameter("Action", typeof(string));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var birthdayStartParameter = birthdayStart.HasValue ?
                new ObjectParameter("BirthdayStart", birthdayStart) :
                new ObjectParameter("BirthdayStart", typeof(int));
    
            var birthdayEndParameter = birthdayEnd.HasValue ?
                new ObjectParameter("BirthdayEnd", birthdayEnd) :
                new ObjectParameter("BirthdayEnd", typeof(int));
    
            var classeParameter = classe != null ?
                new ObjectParameter("Classe", classe) :
                new ObjectParameter("Classe", typeof(string));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var iD_GroupParameter = iD_Group.HasValue ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(System.Guid));
    
            var sessoParameter = sesso != null ?
                new ObjectParameter("Sesso", sesso) :
                new ObjectParameter("Sesso", typeof(string));
    
            var weeksParameter = weeks != null ?
                new ObjectParameter("Weeks", weeks) :
                new ObjectParameter("Weeks", typeof(string));
    
            var ruoloParameter = ruolo.HasValue ?
                new ObjectParameter("ruolo", ruolo) :
                new ObjectParameter("ruolo", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spInputOutputGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, dateStartParameter, dateEndParameter, iD_GrestParameter, actionParameter, iD_UserParameter, birthdayStartParameter, birthdayEndParameter, classeParameter, iD_DinnerParameter, iD_GroupParameter, sessoParameter, weeksParameter, ruoloParameter);
        }
    
        public virtual int spInputOutputGetsTotal(string sSearch, Nullable<System.DateTime> dateStart, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, string action, Nullable<System.Guid> iD_User, Nullable<int> birthdayStart, Nullable<int> birthdayEnd, string classe, Nullable<int> iD_Dinner, Nullable<System.Guid> iD_Group, string weeks)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateStartParameter = dateStart.HasValue ?
                new ObjectParameter("DateStart", dateStart) :
                new ObjectParameter("DateStart", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var actionParameter = action != null ?
                new ObjectParameter("Action", action) :
                new ObjectParameter("Action", typeof(string));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var birthdayStartParameter = birthdayStart.HasValue ?
                new ObjectParameter("BirthdayStart", birthdayStart) :
                new ObjectParameter("BirthdayStart", typeof(int));
    
            var birthdayEndParameter = birthdayEnd.HasValue ?
                new ObjectParameter("BirthdayEnd", birthdayEnd) :
                new ObjectParameter("BirthdayEnd", typeof(int));
    
            var classeParameter = classe != null ?
                new ObjectParameter("Classe", classe) :
                new ObjectParameter("Classe", typeof(string));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var iD_GroupParameter = iD_Group.HasValue ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(System.Guid));
    
            var weeksParameter = weeks != null ?
                new ObjectParameter("Weeks", weeks) :
                new ObjectParameter("Weeks", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spInputOutputGetsTotal", sSearchParameter, dateStartParameter, dateEndParameter, iD_GrestParameter, actionParameter, iD_UserParameter, birthdayStartParameter, birthdayEndParameter, classeParameter, iD_DinnerParameter, iD_GroupParameter, weeksParameter);
        }
    
        public virtual int spListinoGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Prodotto, Nullable<int> userType, Nullable<System.Guid> userId)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_ProdottoParameter = iD_Prodotto.HasValue ?
                new ObjectParameter("ID_Prodotto", iD_Prodotto) :
                new ObjectParameter("ID_Prodotto", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spListinoGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_ProdottoParameter, userTypeParameter, userIdParameter);
        }
    
        public virtual int spListinoGetsTotal(string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Prodotto, Nullable<int> userType)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_ProdottoParameter = iD_Prodotto.HasValue ?
                new ObjectParameter("ID_Prodotto", iD_Prodotto) :
                new ObjectParameter("ID_Prodotto", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spListinoGetsTotal", sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_ProdottoParameter, userTypeParameter);
        }
    
        public virtual ObjectResult<Nullable<int>> spListinoMaxIdOrdine(Nullable<System.Guid> iD_Grest, Nullable<int> iD_Year)
        {
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction<Nullable<int>>("spListinoMaxIdOrdine", iD_GrestParameter, iD_YearParameter);
        }
    
        public virtual int spLogGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.Guid> iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spLogGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spLogGetsTotal(string sSearch, Nullable<System.Guid> iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spLogGetsTotal", sSearchParameter, iD_GrestParameter);
        }
    
        public virtual int spMensaGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Dinner, Nullable<int> userType, Nullable<System.Guid> userId, string gender, string iD_Group, Nullable<bool> only_Intolleranti)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(System.Guid));
    
            var genderParameter = gender != null ?
                new ObjectParameter("gender", gender) :
                new ObjectParameter("gender", typeof(string));
    
            var iD_GroupParameter = iD_Group != null ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(string));
    
            var only_IntollerantiParameter = only_Intolleranti.HasValue ?
                new ObjectParameter("Only_Intolleranti", only_Intolleranti) :
                new ObjectParameter("Only_Intolleranti", typeof(bool));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spMensaGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_DinnerParameter, userTypeParameter, userIdParameter, genderParameter, iD_GroupParameter, only_IntollerantiParameter);
        }
    
        public virtual int spMensaGetsTotal(string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Dinner, Nullable<int> userType, Nullable<System.Guid> userId, string gender, string iD_Group)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            var userIdParameter = userId.HasValue ?
                new ObjectParameter("UserId", userId) :
                new ObjectParameter("UserId", typeof(System.Guid));
    
            var genderParameter = gender != null ?
                new ObjectParameter("gender", gender) :
                new ObjectParameter("gender", typeof(string));
    
            var iD_GroupParameter = iD_Group != null ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spMensaGetsTotal", sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_DinnerParameter, userTypeParameter, userIdParameter, genderParameter, iD_GroupParameter);
        }
    
        public virtual int spMensaGetsTotal_old(string sSearch, Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Dinner, Nullable<int> userType)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spMensaGetsTotal_old", sSearchParameter, dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_DinnerParameter, userTypeParameter);
        }
    
        public virtual int spMensaGetsTotalEat(Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Dinner, Nullable<int> userType)
        {
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spMensaGetsTotalEat", dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_DinnerParameter, userTypeParameter);
        }
    
        public virtual int spMensaGetsTotalIntolleranti(Nullable<System.DateTime> dateInsert, Nullable<System.DateTime> dateEnd, Nullable<System.Guid> iD_Grest, Nullable<int> iD_Dinner, Nullable<int> userType)
        {
            var dateInsertParameter = dateInsert.HasValue ?
                new ObjectParameter("DateInsert", dateInsert) :
                new ObjectParameter("DateInsert", typeof(System.DateTime));
    
            var dateEndParameter = dateEnd.HasValue ?
                new ObjectParameter("DateEnd", dateEnd) :
                new ObjectParameter("DateEnd", typeof(System.DateTime));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_DinnerParameter = iD_Dinner.HasValue ?
                new ObjectParameter("ID_Dinner", iD_Dinner) :
                new ObjectParameter("ID_Dinner", typeof(int));
    
            var userTypeParameter = userType.HasValue ?
                new ObjectParameter("UserType", userType) :
                new ObjectParameter("UserType", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spMensaGetsTotalIntolleranti", dateInsertParameter, dateEndParameter, iD_GrestParameter, iD_DinnerParameter, userTypeParameter);
        }
    
        public virtual int spNewsGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest, string enable, Nullable<int> year)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var enableParameter = enable != null ?
                new ObjectParameter("Enable", enable) :
                new ObjectParameter("Enable", typeof(string));
    
            var yearParameter = year.HasValue ?
                new ObjectParameter("Year", year) :
                new ObjectParameter("Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spNewsGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter, enableParameter, yearParameter);
        }
    
        public virtual int spNewsGetsTotal(string sSearch, string iD_Grest, string enable, Nullable<int> year)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var enableParameter = enable != null ?
                new ObjectParameter("Enable", enable) :
                new ObjectParameter("Enable", typeof(string));
    
            var yearParameter = year.HasValue ?
                new ObjectParameter("Year", year) :
                new ObjectParameter("Year", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spNewsGetsTotal", sSearchParameter, iD_GrestParameter, enableParameter, yearParameter);
        }
    
        public virtual int spRelUserChild(string iD_Child)
        {
            var iD_ChildParameter = iD_Child != null ?
                new ObjectParameter("ID_Child", iD_Child) :
                new ObjectParameter("ID_Child", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spRelUserChild", iD_ChildParameter);
        }
    
        public virtual int spTripBusGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<System.Guid> iD_Trip)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripBusGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_TripParameter);
        }
    
        public virtual int spTripGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spTripGetsBusPA(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Bus)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_BusParameter = iD_Bus.HasValue ?
                new ObjectParameter("ID_Bus", iD_Bus) :
                new ObjectParameter("ID_Bus", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsBusPA", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_UserTypeParameter, iD_BusParameter);
        }
    
        public virtual int spTripGetsBusTotal(string sSearch, Nullable<System.Guid> iD_Trip)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsBusTotal", sSearchParameter, iD_TripParameter);
        }
    
        public virtual int spTripGetsIscrizione(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User, Nullable<int> ageFrom, Nullable<int> ageTo)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            var ageFromParameter = ageFrom.HasValue ?
                new ObjectParameter("ageFrom", ageFrom) :
                new ObjectParameter("ageFrom", typeof(int));
    
            var ageToParameter = ageTo.HasValue ?
                new ObjectParameter("ageTo", ageTo) :
                new ObjectParameter("ageTo", typeof(int));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsIscrizione", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter, iD_UserParameter, ageFromParameter, ageToParameter);
        }
    
        public virtual int spTripGetsPA(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Trip)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsPA", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_UserTypeParameter, iD_TripParameter);
        }
    
        public virtual int spTripGetsPABus(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Group, string sesso, Nullable<System.Guid> iD_Trip)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_GroupParameter = iD_Group.HasValue ?
                new ObjectParameter("ID_Group", iD_Group) :
                new ObjectParameter("ID_Group", typeof(System.Guid));
    
            var sessoParameter = sesso != null ?
                new ObjectParameter("Sesso", sesso) :
                new ObjectParameter("Sesso", typeof(string));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsPABus", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_UserTypeParameter, iD_GroupParameter, sessoParameter, iD_TripParameter);
        }
    
        public virtual int spTripGetsTotal(string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsTotal", sSearchParameter, iD_YearParameter, iD_GrestParameter);
        }
    
        public virtual int spTripGetsTotalBusPA(string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Bus)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_BusParameter = iD_Bus.HasValue ?
                new ObjectParameter("ID_Bus", iD_Bus) :
                new ObjectParameter("ID_Bus", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsTotalBusPA", sSearchParameter, iD_UserTypeParameter, iD_BusParameter);
        }
    
        public virtual int spTripGetsTotalPA(string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Trip)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsTotalPA", sSearchParameter, iD_UserTypeParameter, iD_TripParameter);
        }
    
        public virtual int spTripGetsTotalPABus(string sSearch, Nullable<int> iD_UserType, Nullable<System.Guid> iD_Trip)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_TripParameter = iD_Trip.HasValue ?
                new ObjectParameter("ID_Trip", iD_Trip) :
                new ObjectParameter("ID_Trip", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsTotalPABus", sSearchParameter, iD_UserTypeParameter, iD_TripParameter);
        }
    
        public virtual int spTripGetsUser(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, Nullable<int> iD_Year, Nullable<System.Guid> iD_Grest, Nullable<System.Guid> iD_User)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var iD_GrestParameter = iD_Grest.HasValue ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(System.Guid));
    
            var iD_UserParameter = iD_User.HasValue ?
                new ObjectParameter("ID_User", iD_User) :
                new ObjectParameter("ID_User", typeof(System.Guid));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spTripGetsUser", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_YearParameter, iD_GrestParameter, iD_UserParameter);
        }
    
        public virtual int spUserGets(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest, string isListAdmin, Nullable<int> iD_UserType, Nullable<int> iD_Year, Nullable<int> ageFrom, Nullable<int> ageTo, string gender, string classe, string weeks, string enable, Nullable<bool> creditoNegativo, string page, Nullable<System.Guid> id_group, string id_trip)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var isListAdminParameter = isListAdmin != null ?
                new ObjectParameter("isListAdmin", isListAdmin) :
                new ObjectParameter("isListAdmin", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var ageFromParameter = ageFrom.HasValue ?
                new ObjectParameter("AgeFrom", ageFrom) :
                new ObjectParameter("AgeFrom", typeof(int));
    
            var ageToParameter = ageTo.HasValue ?
                new ObjectParameter("AgeTo", ageTo) :
                new ObjectParameter("AgeTo", typeof(int));
    
            var genderParameter = gender != null ?
                new ObjectParameter("Gender", gender) :
                new ObjectParameter("Gender", typeof(string));
    
            var classeParameter = classe != null ?
                new ObjectParameter("Classe", classe) :
                new ObjectParameter("Classe", typeof(string));
    
            var weeksParameter = weeks != null ?
                new ObjectParameter("Weeks", weeks) :
                new ObjectParameter("Weeks", typeof(string));
    
            var enableParameter = enable != null ?
                new ObjectParameter("Enable", enable) :
                new ObjectParameter("Enable", typeof(string));
    
            var creditoNegativoParameter = creditoNegativo.HasValue ?
                new ObjectParameter("creditoNegativo", creditoNegativo) :
                new ObjectParameter("creditoNegativo", typeof(bool));
    
            var pageParameter = page != null ?
                new ObjectParameter("page", page) :
                new ObjectParameter("page", typeof(string));
    
            var id_groupParameter = id_group.HasValue ?
                new ObjectParameter("id_group", id_group) :
                new ObjectParameter("id_group", typeof(System.Guid));
    
            var id_tripParameter = id_trip != null ?
                new ObjectParameter("id_trip", id_trip) :
                new ObjectParameter("id_trip", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spUserGets", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter, isListAdminParameter, iD_UserTypeParameter, iD_YearParameter, ageFromParameter, ageToParameter, genderParameter, classeParameter, weeksParameter, enableParameter, creditoNegativoParameter, pageParameter, id_groupParameter, id_tripParameter);
        }
    
        public virtual int spUserGetsFilter(Nullable<int> skip, Nullable<int> take, Nullable<int> iSortCol, string iSortDir, string sSearch, string iD_Grest, string isListAdmin, Nullable<int> iD_UserType, Nullable<int> iD_Year, Nullable<int> ageFrom, Nullable<int> ageTo, string gender, string classe)
        {
            var skipParameter = skip.HasValue ?
                new ObjectParameter("Skip", skip) :
                new ObjectParameter("Skip", typeof(int));
    
            var takeParameter = take.HasValue ?
                new ObjectParameter("Take", take) :
                new ObjectParameter("Take", typeof(int));
    
            var iSortColParameter = iSortCol.HasValue ?
                new ObjectParameter("iSortCol", iSortCol) :
                new ObjectParameter("iSortCol", typeof(int));
    
            var iSortDirParameter = iSortDir != null ?
                new ObjectParameter("iSortDir", iSortDir) :
                new ObjectParameter("iSortDir", typeof(string));
    
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var isListAdminParameter = isListAdmin != null ?
                new ObjectParameter("isListAdmin", isListAdmin) :
                new ObjectParameter("isListAdmin", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var ageFromParameter = ageFrom.HasValue ?
                new ObjectParameter("AgeFrom", ageFrom) :
                new ObjectParameter("AgeFrom", typeof(int));
    
            var ageToParameter = ageTo.HasValue ?
                new ObjectParameter("AgeTo", ageTo) :
                new ObjectParameter("AgeTo", typeof(int));
    
            var genderParameter = gender != null ?
                new ObjectParameter("Gender", gender) :
                new ObjectParameter("Gender", typeof(string));
    
            var classeParameter = classe != null ?
                new ObjectParameter("Classe", classe) :
                new ObjectParameter("Classe", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spUserGetsFilter", skipParameter, takeParameter, iSortColParameter, iSortDirParameter, sSearchParameter, iD_GrestParameter, isListAdminParameter, iD_UserTypeParameter, iD_YearParameter, ageFromParameter, ageToParameter, genderParameter, classeParameter);
        }
    
        public virtual int spUserGetsTotal(string sSearch, string iD_Grest, string isListAdmin, Nullable<int> iD_UserType, Nullable<int> iD_Year, string weeks, string enable, Nullable<System.Guid> id_group, string id_trip)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var isListAdminParameter = isListAdmin != null ?
                new ObjectParameter("isListAdmin", isListAdmin) :
                new ObjectParameter("isListAdmin", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var weeksParameter = weeks != null ?
                new ObjectParameter("Weeks", weeks) :
                new ObjectParameter("Weeks", typeof(string));
    
            var enableParameter = enable != null ?
                new ObjectParameter("Enable", enable) :
                new ObjectParameter("Enable", typeof(string));
    
            var id_groupParameter = id_group.HasValue ?
                new ObjectParameter("id_group", id_group) :
                new ObjectParameter("id_group", typeof(System.Guid));
    
            var id_tripParameter = id_trip != null ?
                new ObjectParameter("id_trip", id_trip) :
                new ObjectParameter("id_trip", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spUserGetsTotal", sSearchParameter, iD_GrestParameter, isListAdminParameter, iD_UserTypeParameter, iD_YearParameter, weeksParameter, enableParameter, id_groupParameter, id_tripParameter);
        }
    
        public virtual int spUserGetsTotalFilter(string sSearch, string iD_Grest, string isListAdmin, Nullable<int> iD_UserType, Nullable<int> iD_Year, Nullable<int> ageFrom, Nullable<int> ageTo, string gender, string classe)
        {
            var sSearchParameter = sSearch != null ?
                new ObjectParameter("sSearch", sSearch) :
                new ObjectParameter("sSearch", typeof(string));
    
            var iD_GrestParameter = iD_Grest != null ?
                new ObjectParameter("ID_Grest", iD_Grest) :
                new ObjectParameter("ID_Grest", typeof(string));
    
            var isListAdminParameter = isListAdmin != null ?
                new ObjectParameter("isListAdmin", isListAdmin) :
                new ObjectParameter("isListAdmin", typeof(string));
    
            var iD_UserTypeParameter = iD_UserType.HasValue ?
                new ObjectParameter("ID_UserType", iD_UserType) :
                new ObjectParameter("ID_UserType", typeof(int));
    
            var iD_YearParameter = iD_Year.HasValue ?
                new ObjectParameter("ID_Year", iD_Year) :
                new ObjectParameter("ID_Year", typeof(int));
    
            var ageFromParameter = ageFrom.HasValue ?
                new ObjectParameter("AgeFrom", ageFrom) :
                new ObjectParameter("AgeFrom", typeof(int));
    
            var ageToParameter = ageTo.HasValue ?
                new ObjectParameter("AgeTo", ageTo) :
                new ObjectParameter("AgeTo", typeof(int));
    
            var genderParameter = gender != null ?
                new ObjectParameter("Gender", gender) :
                new ObjectParameter("Gender", typeof(string));
    
            var classeParameter = classe != null ?
                new ObjectParameter("Classe", classe) :
                new ObjectParameter("Classe", typeof(string));
    
            return ((IObjectContextAdapter)this).ObjectContext.ExecuteFunction("spUserGetsTotalFilter", sSearchParameter, iD_GrestParameter, isListAdminParameter, iD_UserTypeParameter, iD_YearParameter, ageFromParameter, ageToParameter, genderParameter, classeParameter);
        }
    }
}
