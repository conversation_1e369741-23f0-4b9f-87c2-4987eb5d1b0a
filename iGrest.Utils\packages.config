﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net48" />
  <package id="FluentFTP" version="49.0.2" targetFramework="net48" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Options" version="2.0.0" targetFramework="net48" />
  <package id="Microsoft.Extensions.Primitives" version="2.0.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="Serilog" version="2.12.0" targetFramework="net48" />
  <package id="Serilog.Extensions.Logging" version="3.1.0" targetFramework="net48" />
  <package id="Serilog.Sinks.File" version="5.0.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="4.4.0" targetFramework="net48" />
</packages>