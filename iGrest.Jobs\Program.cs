﻿using iGrest.Jobs.Commands;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using iGrest.Core;
using System.Data.Entity;

namespace iGrest.Jobs
{
	internal class Program
	{
		private static IDictionary<string, ICommand> Commands = new Dictionary<string, ICommand>();
		
		static void Main(string[] args)
		{
			// If no arguments provided, run Entity Framework connection test
			if (args.Length == 0)
			{
				TestEntityFrameworkConnection();
				return;
			}

			init();

			Commands[args[0]].Execute(args);
		}

		static void TestEntityFrameworkConnection()
		{
			Console.WriteLine("=== Entity Framework Connection Test ===");
			Console.WriteLine("Testing connection to iGrest database...");
			
			try
			{
				using (var context = new iGrestEntities())
				{
					Console.WriteLine("✓ Entity Framework context created successfully");
					
					// Test if database exists
					bool dbExists = context.Database.Exists();
					Console.WriteLine("✓ Database exists: " + dbExists);
					
					// Test connection string
					Console.WriteLine("✓ Connection string: " + context.Database.Connection.ConnectionString);
					
					// Try to access a simple entity (if database exists)
					if (dbExists)
					{
						try
						{
							var count = context.Grest.Count();
							Console.WriteLine("✓ Successfully queried Grest table. Count: " + count);
						}
						catch (Exception ex)
						{
							Console.WriteLine("⚠ Warning: Could not query Grest table: " + ex.Message);
						}
					}
					
					Console.WriteLine("✓ Entity Framework connection test completed successfully!");
					Console.WriteLine("✓ No UnintentionalCodeFirstException occurred - the fix is working!");
				}
			}
			catch (System.Data.Entity.Infrastructure.UnintentionalCodeFirstException ex)
			{
				Console.WriteLine("✗ FAILED: UnintentionalCodeFirstException occurred: " + ex.Message);
				Console.WriteLine("This indicates the Entity Framework configuration fix did not work.");
			}
			catch (System.Data.Entity.Core.MetadataException ex)
			{
				Console.WriteLine("✗ FAILED: MetadataException (EDMX issue): " + ex.Message);
			}
			catch (Exception ex)
			{
				Console.WriteLine("⚠ Other exception occurred: " + ex.GetType().Name + ": " + ex.Message);
			}
			
			Console.WriteLine("\nPress any key to exit...");
			Console.ReadKey();
		}

		static void init()
		{
			var loggerFactory = new LoggerFactory();
			loggerFactory.AddSerilog(new LoggerConfiguration()
				.WriteTo.File("logs\\iGrest_jobs.log", rollingInterval: RollingInterval.Day)
				.MinimumLevel.Warning()
				.CreateLogger());

			Commands.Add(CommandType.FileExpirations, new FileExpirationsCommand(loggerFactory));

			      Commands.Add(CommandType.AlignSubscriptionAttachment, new AlignSubscriptionAttachmentCommand(loggerFactory));

			// NUOVO: Comando per cleanup automatico dei log
			Commands.Add(CommandType.LogCleanup, new LogCleanupCommand(loggerFactory));
			   }
	}
}
