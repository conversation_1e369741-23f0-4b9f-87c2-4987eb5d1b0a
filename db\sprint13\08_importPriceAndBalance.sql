
-- IMPORT PRICES

DELETE FROM [dbo].TripsPrice

insert into [dbo].TripsPrice
(ID_TripMaster, PaymentDate, Price, ID_UserType, Description)
select ID_TripMaster, StartDate, DepositPrice, 4, 'Acconto: ' + Name
from [dbo].TripsMaster
where DepositPrice is not null and DepositPrice > 0
union all
select distinct ID_TripMaster, (case when isnull(DepositPrice, 0) = 0 then StartDate else EndDate end), isnull(UnitPrice, 0), 4, Name
from [dbo].TripsMaster



insert into [dbo].TripsPrice
(ID_TripMaster, PaymentDate, Price, ID_UserType, Description)
select ID_TripMaster, StartDate, DepositPriceEducator, 5, 'Acconto: ' + Name
from [dbo].TripsMaster
where DepositPriceEducator is not null and DepositPriceEducator > 0
union all
select distinct ID_TripMaster, (case when isnull(DepositPriceEducator, 0) = 0 then StartDate else EndDate end), isnull(UnitPriceEducator, 0), 5, Name
from [dbo].TripsMaster



-- IMPORT MOVEMENTS

DELETE FROM [dbo].Rel_Users_TripsPrice

insert into [dbo].Rel_Users_TripsPrice
(ID_User, ID_TripPrice, ID_Balance, ID_Trip)
select distinct rut.ID_User, tp.ID_TripPrice, rut.ID_Balance, rut.ID_Trip
from [dbo].Rel_Users_Trip rut
inner join [dbo].Trips t on t.ID_Trip = rut.ID_Trip
inner join [dbo].TripsPrice tp on tp.ID_TripMaster = t.ID_TripMaster and tp.ID_UserType = rut.ID_UserType 
	and ((tp.Price = t.UnitPrice and tp.ID_UserType = 4) or (tp.Price = t.UnitPriceEducator and tp.ID_UserType = 5))
where rut.ID_Balance is not null and tp.Price > 0
order by rut.ID_Balance

insert into [dbo].Rel_Users_TripsPrice
(ID_User, ID_TripPrice, ID_Balance, ID_Trip)
select distinct rut.ID_User, tp.ID_TripPrice, rut.ID_Balance_Deposit, rut.ID_Trip
from [dbo].Rel_Users_Trip rut
inner join [dbo].Trips t on t.ID_Trip = rut.ID_Trip
inner join [dbo].TripsPrice tp on tp.ID_TripMaster = t.ID_TripMaster and tp.ID_UserType = rut.ID_UserType 
	and ((tp.Price = t.DepositPrice and tp.ID_UserType = 4) or (tp.Price = t.DepositPriceEducator and tp.ID_UserType = 5))
where rut.ID_Balance_Deposit is not null and tp.Price > 0
order by rut.ID_Balance_Deposit