//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Rel_Users_Trip
    {
        public int ID { get; set; }
        public System.Guid ID_Trip { get; set; }
        public System.Guid ID_User { get; set; }
        public int ID_UserType { get; set; }
        public Nullable<int> Position { get; set; }
        public bool Confirmed { get; set; }
        public Nullable<System.DateTime> DateInsert { get; set; }
        public Nullable<System.DateTime> DateConfirm { get; set; }
        public string Note { get; set; }
    
        public virtual Users Users { get; set; }
        public virtual UserType UserType { get; set; }
        public virtual Trips Trips { get; set; }
    }
}
