using System;
using System.Data.SqlClient;
using System.Data;
using System.Text;

namespace iGrest.DatabaseAnalyzer
{
    class DatabaseSizeAnalyzer
    {
        private static string connectionStringTemplate = "Data Source=.\\SQLEXPRESS,1433;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;Initial Catalog={0}";
        private static string masterConnectionString = "Data Source=.\\SQLEXPRESS,1433;User ID=igrest_app;Password=iGrest2025!Secure;MultipleActiveResultSets=True;Connect Timeout=30;Encrypt=False;TrustServerCertificate=True;Initial Catalog=master";

        static void Main(string[] args)
        {
            Console.WriteLine("=== ANALISI DIMENSIONI DATABASE SQL SERVER ===");
            Console.WriteLine("Data/Ora: " + DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss"));
            Console.WriteLine();

            try
            {
                // Prima verifica quali database esistono
                var databases = GetAvailableDatabases();
                
                if (databases.Count == 0)
                {
                    Console.WriteLine("Nessun database trovato o errore di connessione.");
                    return;
                }

                Console.WriteLine("DATABASE TROVATI:");
                foreach (var db in databases)
                {
                    Console.WriteLine("- " + db);
                }
                Console.WriteLine();

                // Analizza ogni database che contiene "igrest" nel nome
                foreach (var dbName in databases)
                {
                    if (dbName.ToLower().Contains("igrest"))
                    {
                        Console.WriteLine("=== ANALISI DATABASE: " + dbName + " ===");
                        AnalyzeDatabaseSize(dbName);
                        Console.WriteLine();
                    }
                }

                // Se non trova database con "igrest", analizza comunque iGrest
                if (!databases.Exists(db => db.ToLower().Contains("igrest")))
                {
                    Console.WriteLine("=== TENTATIVO ANALISI DATABASE: iGrest ===");
                    AnalyzeDatabaseSize("iGrest");
                }

                Console.WriteLine("=== CONFRONTO COMPLETATO ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine("ERRORE GENERALE: " + ex.Message);
                Console.WriteLine("Stack Trace: " + ex.StackTrace);
            }

            Console.WriteLine("\nPremi un tasto per uscire...");
            Console.ReadKey();
        }

        private static System.Collections.Generic.List<string> GetAvailableDatabases()
        {
            var databases = new System.Collections.Generic.List<string>();
            
            try
            {
                using (var connection = new SqlConnection(masterConnectionString))
                {
                    connection.Open();
                    Console.WriteLine("✓ Connessione al server SQL Server riuscita");
                    
                    string query = @"
                        SELECT name 
                        FROM sys.databases 
                        WHERE name NOT IN ('master', 'tempdb', 'model', 'msdb')
                        ORDER BY name";
                    
                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            databases.Add(reader["name"].ToString());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("✗ Errore nel recupero dei database: " + ex.Message);
            }
            
            return databases;
        }

        private static void AnalyzeDatabaseSize(string databaseName)
        {
            try
            {
                string connectionString = string.Format(connectionStringTemplate, databaseName);
                
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✓ Connesso al database: " + databaseName);

                    // 1. Informazioni generali del database
                    GetDatabaseGeneralInfo(connection, databaseName);
                    
                    // 2. Dimensioni dei file
                    GetDatabaseFilesSizes(connection);
                    
                    // 3. Spazio utilizzato vs allocato
                    GetSpaceUsage(connection);
                    
                    // 4. Dimensioni delle tabelle principali
                    GetTableSizes(connection);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("✗ Errore nell'analisi del database " + databaseName + ": " + ex.Message);
            }
        }

        private static void GetDatabaseGeneralInfo(SqlConnection connection, string databaseName)
        {
            try
            {
                string query = @"
                    SELECT 
                        DB_NAME() as DatabaseName,
                        CAST(SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024 AS DECIMAL(15,2)) AS UsedSpaceMB,
                        CAST(SUM(size) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS AllocatedSpaceMB,
                        CAST((SUM(size) * 8192. / 1024 / 1024) - (SUM(CAST(FILEPROPERTY(name, 'SpaceUsed') AS bigint) * 8192.) / 1024 / 1024) AS DECIMAL(15,2)) AS FreeSpaceMB
                    FROM sys.database_files";

                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        Console.WriteLine("INFORMAZIONI GENERALI:");
                        Console.WriteLine("  Nome Database: " + reader["DatabaseName"]);
                        Console.WriteLine("  Spazio Utilizzato: " + reader["UsedSpaceMB"] + " MB");
                        Console.WriteLine("  Spazio Allocato: " + reader["AllocatedSpaceMB"] + " MB");
                        Console.WriteLine("  Spazio Libero: " + reader["FreeSpaceMB"] + " MB");
                        
                        decimal used = Convert.ToDecimal(reader["UsedSpaceMB"]);
                        decimal allocated = Convert.ToDecimal(reader["AllocatedSpaceMB"]);
                        decimal percentage = allocated > 0 ? (used / allocated) * 100 : 0;
                        Console.WriteLine("  Percentuale Utilizzo: " + percentage.ToString("F1") + "%");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Errore nel recupero informazioni generali: " + ex.Message);
            }
        }

        private static void GetDatabaseFilesSizes(SqlConnection connection)
        {
            try
            {
                string query = @"
                    SELECT 
                        name AS FileName,
                        type_desc AS FileType,
                        physical_name AS PhysicalPath,
                        CAST(size * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS SizeMB,
                        CAST(FILEPROPERTY(name, 'SpaceUsed') * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS UsedMB,
                        CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS FreeMB
                    FROM sys.database_files
                    ORDER BY type, name";

                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\nDIMENSIONI DEI FILE:");
                    while (reader.Read())
                    {
                        Console.WriteLine("  File: " + reader["FileName"]);
                        Console.WriteLine("    Tipo: " + reader["FileType"]);
                        Console.WriteLine("    Percorso: " + reader["PhysicalPath"]);
                        Console.WriteLine("    Dimensione: " + reader["SizeMB"] + " MB");
                        Console.WriteLine("    Utilizzato: " + reader["UsedMB"] + " MB");
                        Console.WriteLine("    Libero: " + reader["FreeMB"] + " MB");
                        Console.WriteLine();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Errore nel recupero dimensioni file: " + ex.Message);
            }
        }

        private static void GetSpaceUsage(SqlConnection connection)
        {
            try
            {
                string query = @"
                    SELECT 
                        'Data' as SpaceType,
                        CAST(SUM(CASE WHEN type = 0 THEN size END) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS AllocatedMB,
                        CAST(SUM(CASE WHEN type = 0 THEN FILEPROPERTY(name, 'SpaceUsed') END) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS UsedMB
                    FROM sys.database_files
                    WHERE type = 0
                    UNION ALL
                    SELECT 
                        'Log' as SpaceType,
                        CAST(SUM(CASE WHEN type = 1 THEN size END) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS AllocatedMB,
                        CAST(SUM(CASE WHEN type = 1 THEN FILEPROPERTY(name, 'SpaceUsed') END) * 8192. / 1024 / 1024 AS DECIMAL(15,2)) AS UsedMB
                    FROM sys.database_files
                    WHERE type = 1";

                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("UTILIZZO SPAZIO PER TIPO:");
                    while (reader.Read())
                    {
                        string spaceType = reader["SpaceType"].ToString();
                        decimal allocated = reader["AllocatedMB"] != DBNull.Value ? Convert.ToDecimal(reader["AllocatedMB"]) : 0;
                        decimal used = reader["UsedMB"] != DBNull.Value ? Convert.ToDecimal(reader["UsedMB"]) : 0;
                        decimal percentage = allocated > 0 ? (used / allocated) * 100 : 0;
                        
                        Console.WriteLine("  " + spaceType + ":");
                        Console.WriteLine("    Allocato: " + allocated + " MB");
                        Console.WriteLine("    Utilizzato: " + used + " MB");
                        Console.WriteLine("    Utilizzo: " + percentage.ToString("F1") + "%");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Errore nel recupero utilizzo spazio: " + ex.Message);
            }
        }

        private static void GetTableSizes(SqlConnection connection)
        {
            try
            {
                string query = @"
                    SELECT TOP 20
                        t.NAME AS TableName,
                        s.Name AS SchemaName,
                        p.rows AS RowCounts,
                        CAST(ROUND(((SUM(a.total_pages) * 8) / 1024.00), 2) AS DECIMAL(15,2)) AS TotalSpaceMB,
                        CAST(ROUND(((SUM(a.used_pages) * 8) / 1024.00), 2) AS DECIMAL(15,2)) AS UsedSpaceMB,
                        CAST(ROUND(((SUM(a.total_pages) - SUM(a.used_pages)) * 8) / 1024.00, 2) AS DECIMAL(15,2)) AS UnusedSpaceMB
                    FROM 
                        sys.tables t
                    INNER JOIN      
                        sys.indexes i ON t.OBJECT_ID = i.object_id
                    INNER JOIN 
                        sys.partitions p ON i.object_id = p.OBJECT_ID AND i.index_id = p.index_id
                    INNER JOIN 
                        sys.allocation_units a ON p.partition_id = a.container_id
                    LEFT OUTER JOIN 
                        sys.schemas s ON t.schema_id = s.schema_id
                    WHERE 
                        t.NAME NOT LIKE 'dt%' 
                        AND t.is_ms_shipped = 0
                        AND i.OBJECT_ID > 255 
                    GROUP BY 
                        t.Name, s.Name, p.Rows
                    ORDER BY 
                        TotalSpaceMB DESC";

                using (var command = new SqlCommand(query, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\nTABELLE PRINCIPALI (Top 20 per dimensione):");
                    Console.WriteLine(string.Format("{0,-30} {1,-10} {2,-12} {3,-10} {4,-10} {5,-10}", "Tabella", "Schema", "Righe", "Tot MB", "Usato MB", "Libero MB"));
                    Console.WriteLine(new string('-', 90));
                    
                    while (reader.Read())
                    {
                        string tableName = reader["TableName"].ToString();
                        string schemaName = reader["SchemaName"].ToString();
                        long rows = Convert.ToInt64(reader["RowCounts"]);
                        decimal totalMB = Convert.ToDecimal(reader["TotalSpaceMB"]);
                        decimal usedMB = Convert.ToDecimal(reader["UsedSpaceMB"]);
                        decimal unusedMB = Convert.ToDecimal(reader["UnusedSpaceMB"]);
                        
                        Console.WriteLine(string.Format("{0,-30} {1,-10} {2,-12:N0} {3,-10:F2} {4,-10:F2} {5,-10:F2}", tableName, schemaName, rows, totalMB, usedMB, unusedMB));
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Errore nel recupero dimensioni tabelle: " + ex.Message);
            }
        }
    }
}