/****** Object:  Table [dbo].[Rel_Files_Fornitori]    Script Date: 31/01/2025 13:07:45 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Rel_Files_Fornitori](
	[ID_File_Fornitore] [int] IDENTITY(1,1) NOT NULL,
	[ID_File] [int] NOT NULL,
	[ID_Fornitore] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_Rel_Files_Fornitori] PRIMARY KEY CLUSTERED 
(
	[ID_File_Fornitore] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Rel_Files_Fornitori]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Files_Fornitori_Grest_Files] FOREIGN KEY([ID_File])
REFERENCES [dbo].[Grest_Files] ([ID_File])
GO

ALTER TABLE [dbo].[Rel_Files_Fornitori] CHECK CONSTRAINT [FK_Rel_Files_Fornitori_Grest_Files]
GO

ALTER TABLE [dbo].[Rel_Files_Fornitori]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Files_Fornitori_Fornitori] FOREIGN KEY([ID_Fornitore])
REFERENCES [dbo].[Fornitori] ([ID_Fornitore])
GO

ALTER TABLE [dbo].[Rel_Files_Fornitori] CHECK CONSTRAINT [FK_Rel_Files_Fornitori_Fornitori]
GO


