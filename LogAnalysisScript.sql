-- =====================================================
-- SCRIPT DI ANALISI ORIGINE LOG DATABASE iGrest
-- Data: 07/06/2025
-- Scopo: Capire da dove arrivano i log che occupano spazio
-- =====================================================

USE iGrest;
GO

PRINT '=== ANALISI ORIGINE LOG DATABASE iGrest ==='
PRINT 'Data/Ora: ' + CONVERT(VARCHAR, GETDATE(), 120)
PRINT ''

-- =====================================================
-- 1. ANALISI TABELLA LOGS
-- =====================================================

PRINT '1. ANALISI TABELLA LOGS'
PRINT '------------------------'

-- Verifica struttura tabella Logs
PRINT 'Struttura tabella Logs:'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Logs'
ORDER BY ORDINAL_POSITION;

PRINT ''

-- Analisi distribuzione per data
PRINT 'Distribuzione log per data (ultimi 30 giorni):'
SELECT 
    CAST(CreatedDate AS DATE) as Data,
    COUNT(*) as NumeroLog,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Logs) as PercentualeTotale
FROM Logs 
WHERE CreatedDate >= DATEADD(day, -30, GETDATE())
GROUP BY CAST(CreatedDate AS DATE)
ORDER BY Data DESC;

PRINT ''

-- Analisi per tipo di log (se esiste campo Type o Level)
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Logs' AND COLUMN_NAME = 'Type')
BEGIN
    PRINT 'Distribuzione per tipo di log:'
    SELECT 
        Type,
        COUNT(*) as Numero,
        COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Logs) as Percentuale
    FROM Logs 
    GROUP BY Type
    ORDER BY Numero DESC;
    PRINT ''
END

IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Logs' AND COLUMN_NAME = 'Level')
BEGIN
    PRINT 'Distribuzione per livello di log:'
    SELECT 
        Level,
        COUNT(*) as Numero,
        COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Logs) as Percentuale
    FROM Logs 
    GROUP BY Level
    ORDER BY Numero DESC;
    PRINT ''
END

-- Analisi messaggi più frequenti
PRINT 'Top 10 messaggi di log più frequenti:'
SELECT TOP 10
    LEFT(Message, 100) as MessaggioTroncato,
    COUNT(*) as Frequenza,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Logs) as Percentuale
FROM Logs 
GROUP BY LEFT(Message, 100)
ORDER BY Frequenza DESC;

PRINT ''

-- =====================================================
-- 2. ANALISI TABELLA LOG (singolare)
-- =====================================================

PRINT '2. ANALISI TABELLA LOG'
PRINT '----------------------'

-- Verifica struttura tabella Log
PRINT 'Struttura tabella Log:'
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    CHARACTER_MAXIMUM_LENGTH
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Log'
ORDER BY ORDINAL_POSITION;

PRINT ''

-- Analisi distribuzione per data
PRINT 'Distribuzione log per data (ultimi 30 giorni):'
SELECT 
    CAST(CreatedDate AS DATE) as Data,
    COUNT(*) as NumeroLog,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM Log) as PercentualeTotale
FROM Log 
WHERE CreatedDate >= DATEADD(day, -30, GETDATE())
GROUP BY CAST(CreatedDate AS DATE)
ORDER BY Data DESC;

PRINT ''

-- =====================================================
-- 3. ANALISI FILE DI LOG SQL SERVER
-- =====================================================

PRINT '3. ANALISI FILE DI LOG SQL SERVER'
PRINT '----------------------------------'

-- Informazioni sui file di log
SELECT 
    name as NomeFile,
    type_desc as TipoFile,
    physical_name as PercorsoFisico,
    CAST(size * 8.0 / 1024 AS DECIMAL(15,2)) as DimensioneMB,
    CAST(FILEPROPERTY(name, 'SpaceUsed') * 8.0 / 1024 AS DECIMAL(15,2)) as SpazioUsatoMB,
    CAST((size - FILEPROPERTY(name, 'SpaceUsed')) * 8.0 / 1024 AS DECIMAL(15,2)) as SpazioLiberoMB
FROM sys.database_files
WHERE type = 1; -- Solo file di log

PRINT ''

-- Verifica se il database è in modalità FULL recovery
SELECT 
    name as NomeDatabase,
    recovery_model_desc as ModelloRecovery,
    log_reuse_wait_desc as MotivoAttesaRiutilizzoLog
FROM sys.databases 
WHERE name = 'iGrest';

PRINT ''

-- =====================================================
-- 4. ANALISI TRANSAZIONI ATTIVE
-- =====================================================

PRINT '4. ANALISI TRANSAZIONI ATTIVE'
PRINT '------------------------------'

-- Verifica transazioni attive che potrebbero bloccare il log
SELECT 
    session_id,
    transaction_id,
    name as NomeTransazione,
    transaction_begin_time as InizioTransazione,
    DATEDIFF(minute, transaction_begin_time, GETDATE()) as DurataMinuti,
    transaction_type,
    transaction_state
FROM sys.dm_tran_active_transactions t
INNER JOIN sys.dm_tran_session_transactions st ON t.transaction_id = st.transaction_id;

PRINT ''

-- =====================================================
-- 5. RACCOMANDAZIONI BASATE SUI RISULTATI
-- =====================================================

PRINT '5. RACCOMANDAZIONI'
PRINT '------------------'

-- Verifica età dei log nelle tabelle
DECLARE @OldestLogDate DATETIME, @NewestLogDate DATETIME, @LogCount INT

SELECT 
    @OldestLogDate = MIN(CreatedDate),
    @NewestLogDate = MAX(CreatedDate),
    @LogCount = COUNT(*)
FROM Logs;

PRINT 'TABELLA LOGS:'
PRINT '  - Numero totale record: ' + CAST(@LogCount AS VARCHAR)
PRINT '  - Log più vecchio: ' + ISNULL(CONVERT(VARCHAR, @OldestLogDate, 120), 'N/A')
PRINT '  - Log più recente: ' + ISNULL(CONVERT(VARCHAR, @NewestLogDate, 120), 'N/A')

IF @OldestLogDate IS NOT NULL AND DATEDIFF(day, @OldestLogDate, GETDATE()) > 90
BEGIN
    PRINT '  ⚠ RACCOMANDAZIONE: Considerare l''archiviazione di log più vecchi di 90 giorni'
END

SELECT 
    @OldestLogDate = MIN(CreatedDate),
    @NewestLogDate = MAX(CreatedDate),
    @LogCount = COUNT(*)
FROM Log;

PRINT ''
PRINT 'TABELLA LOG:'
PRINT '  - Numero totale record: ' + CAST(@LogCount AS VARCHAR)
PRINT '  - Log più vecchio: ' + ISNULL(CONVERT(VARCHAR, @OldestLogDate, 120), 'N/A')
PRINT '  - Log più recente: ' + ISNULL(CONVERT(VARCHAR, @NewestLogDate, 120), 'N/A')

IF @OldestLogDate IS NOT NULL AND DATEDIFF(day, @OldestLogDate, GETDATE()) > 90
BEGIN
    PRINT '  ⚠ RACCOMANDAZIONE: Considerare l''archiviazione di log più vecchi di 90 giorni'
END

PRINT ''
PRINT 'FILE DI LOG SQL SERVER:'
DECLARE @RecoveryModel NVARCHAR(60)
SELECT @RecoveryModel = recovery_model_desc FROM sys.databases WHERE name = 'iGrest'

IF @RecoveryModel = 'FULL'
BEGIN
    PRINT '  ⚠ Il database è in modalità FULL recovery'
    PRINT '  ⚠ I log delle transazioni si accumulano fino al backup del log'
    PRINT '  ⚠ RACCOMANDAZIONE: Eseguire backup del log regolarmente o cambiare a SIMPLE recovery'
END
ELSE
BEGIN
    PRINT '  ✓ Il database è in modalità ' + @RecoveryModel + ' recovery'
END

PRINT ''
PRINT '=== ANALISI COMPLETATA ==='