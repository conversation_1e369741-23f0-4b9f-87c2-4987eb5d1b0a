<!-- 
CONFIGURAZIONE PER ABILITARE LE OTTIMIZZAZIONI
Aggiungi queste righe nella sezione <appSettings> del tuo web.config
-->

<appSettings>
  <!-- Altre configurazioni esistenti... -->
  
  <!-- OTTIMIZZAZIONI PERFORMANCE -->
  <!-- Abilita l'ottimizzazione per grest_trip_iscritti.aspx (default: false) -->
  <add key="EnableTripUsersOptimization" value="false" />
  
  <!-- Abilita logging dettagliato per debug performance (default: false) -->
  <add key="EnablePerformanceLogging" value="true" />
  
  <!-- Timeout per query ottimizzate in secondi (default: 30) -->
  <add key="OptimizedQueryTimeout" value="30" />
</appSettings>

<!-- 
ISTRUZIONI PER ABILITARE LE OTTIMIZZAZIONI:

1. BACKUP OBBLIGATORIO:
   - Esegui db\backup_before_optimization.sql

2. APPLICA INDICI:
   - Esegui db\optimize_trip_iscritti_indexes.sql

3. ABILITA OTTIMIZZAZIONE:
   - Cambia EnableTripUsersOptimization da "false" a "true"
   - Riavvia l'applicazione

4. TESTA:
   - Apri /admin/grest_trip_iscritti.aspx
   - Verifica che tutto funzioni correttamente
   - Controlla i log per eventuali errori

5. SE CI SONO PROBLEMI:
   - Cambia EnableTripUsersOptimization da "true" a "false"
   - Riavvia l'applicazione
   - Esegui db\rollback_optimization.sql se necessario

MONITORAGGIO:
- Con EnablePerformanceLogging="true" vedrai i tempi di esecuzione nei log
- Controlla sempre i log dopo aver abilitato l'ottimizzazione
-->
