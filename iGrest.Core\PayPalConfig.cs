//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class PayPalConfig
    {
        public int ID_PaypalConfig { get; set; }
        public Nullable<System.Guid> ID_Grest { get; set; }
        public string api_username { get; set; }
        public string api_password { get; set; }
        public string applicationid { get; set; }
        public string api_signature { get; set; }
        public bool Removed { get; set; }
        public string street { get; set; }
        public string cityname { get; set; }
        public string stateorprovince { get; set; }
        public string country { get; set; }
        public Nullable<int> postalcode { get; set; }
        public Nullable<decimal> PercentualeRicarico { get; set; }
        public bool PayPalRicaricheAbilitate { get; set; }
        public string Valuta { get; set; }
        public Nullable<decimal> FixedFee { get; set; }
    
        public virtual Grest Grest { get; set; }
    }
}
