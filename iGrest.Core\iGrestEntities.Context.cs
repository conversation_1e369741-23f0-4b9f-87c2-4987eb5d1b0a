﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Data.Entity;
    using System.Data.Entity.Infrastructure;
    
    public partial class Entities : DbContext
    {
        public Entities()
            : base("name=Entities")
        {
        }
    
        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            throw new UnintentionalCodeFirstException();
        }
    
        public virtual DbSet<Activity> Activity { get; set; }
        public virtual DbSet<BalanceIncomeStatements> BalanceIncomeStatements { get; set; }
        public virtual DbSet<BalanceReceiptDetails> BalanceReceiptDetails { get; set; }
        public virtual DbSet<BalanceSheets> BalanceSheets { get; set; }
        public virtual DbSet<Bar> Bar { get; set; }
        public virtual DbSet<Comuni> Comuni { get; set; }
        public virtual DbSet<Dinners> Dinners { get; set; }
        public virtual DbSet<FE_CondizioniPagamento> FE_CondizioniPagamento { get; set; }
        public virtual DbSet<FE_FormatoTrasmissione> FE_FormatoTrasmissione { get; set; }
        public virtual DbSet<FE_ModalitaPagamento> FE_ModalitaPagamento { get; set; }
        public virtual DbSet<FE_RegimeFiscale> FE_RegimeFiscale { get; set; }
        public virtual DbSet<FE_TipoDocumento> FE_TipoDocumento { get; set; }
        public virtual DbSet<Fornitori> Fornitori { get; set; }
        public virtual DbSet<Grest> Grest { get; set; }
        public virtual DbSet<Grest_Files> Grest_Files { get; set; }
        public virtual DbSet<Grest_Files_Users> Grest_Files_Users { get; set; }
        public virtual DbSet<Grest_FilesType> Grest_FilesType { get; set; }
        public virtual DbSet<Grest_Iscrizione_Type> Grest_Iscrizione_Type { get; set; }
        public virtual DbSet<Grest_Progressivo_Ricevuta> Grest_Progressivo_Ricevuta { get; set; }
        public virtual DbSet<Grest_UserAdditionalDetails> Grest_UserAdditionalDetails { get; set; }
        public virtual DbSet<Grest_Year> Grest_Year { get; set; }
        public virtual DbSet<GrestFatt> GrestFatt { get; set; }
        public virtual DbSet<GrestHeadquarters> GrestHeadquarters { get; set; }
        public virtual DbSet<Groups> Groups { get; set; }
        public virtual DbSet<InputOutput> InputOutput { get; set; }
        public virtual DbSet<Listino> Listino { get; set; }
        public virtual DbSet<Log> Log { get; set; }
        public virtual DbSet<Logs> Logs { get; set; }
        public virtual DbSet<Mensa> Mensa { get; set; }
        public virtual DbSet<News> News { get; set; }
        public virtual DbSet<News_Read> News_Read { get; set; }
        public virtual DbSet<NotificType> NotificType { get; set; }
        public virtual DbSet<Order> Order { get; set; }
        public virtual DbSet<OrderItem> OrderItem { get; set; }
        public virtual DbSet<Pagamenti> Pagamenti { get; set; }
        public virtual DbSet<PaymentType> PaymentType { get; set; }
        public virtual DbSet<PayPalConfig> PayPalConfig { get; set; }
        public virtual DbSet<Portale> Portale { get; set; }
        public virtual DbSet<Prodotti> Prodotti { get; set; }
        public virtual DbSet<ProdottiTipo> ProdottiTipo { get; set; }
        public virtual DbSet<Province> Province { get; set; }
        public virtual DbSet<Regioni> Regioni { get; set; }
        public virtual DbSet<Rel_Users> Rel_Users { get; set; }
        public virtual DbSet<Rel_Users_Activity> Rel_Users_Activity { get; set; }
        public virtual DbSet<Rel_Users_Groups> Rel_Users_Groups { get; set; }
        public virtual DbSet<Rel_Users_Headquarter> Rel_Users_Headquarter { get; set; }
        public virtual DbSet<Rel_Users_News> Rel_Users_News { get; set; }
        public virtual DbSet<REL_Users_NotificPlayers> REL_Users_NotificPlayers { get; set; }
        public virtual DbSet<Rel_Users_Trip> Rel_Users_Trip { get; set; }
        public virtual DbSet<Rel_Users_Trip_Bus> Rel_Users_Trip_Bus { get; set; }
        public virtual DbSet<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        public virtual DbSet<Rel_Users_Year> Rel_Users_Year { get; set; }
        public virtual DbSet<SatispayConfig> SatispayConfig { get; set; }
        public virtual DbSet<SatispayTransaction> SatispayTransaction { get; set; }
        public virtual DbSet<SchoolClass> SchoolClass { get; set; }
        public virtual DbSet<StripeConfig> StripeConfig { get; set; }
        public virtual DbSet<StripeTransaction> StripeTransaction { get; set; }
        public virtual DbSet<TransactionType> TransactionType { get; set; }
        public virtual DbSet<Triage> Triage { get; set; }
        public virtual DbSet<Triage_Categorie> Triage_Categorie { get; set; }
        public virtual DbSet<Triage_InputOutput> Triage_InputOutput { get; set; }
        public virtual DbSet<Trip_Bus> Trip_Bus { get; set; }
        public virtual DbSet<Trips> Trips { get; set; }
        public virtual DbSet<TripsMaster> TripsMaster { get; set; }
        public virtual DbSet<TripsPrice> TripsPrice { get; set; }
        public virtual DbSet<TripType> TripType { get; set; }
        public virtual DbSet<Users> Users { get; set; }
        public virtual DbSet<Users_AdditionalDetails> Users_AdditionalDetails { get; set; }
        public virtual DbSet<UserType> UserType { get; set; }
        public virtual DbSet<W_BalanceUsers> W_BalanceUsers { get; set; }
    }
}
