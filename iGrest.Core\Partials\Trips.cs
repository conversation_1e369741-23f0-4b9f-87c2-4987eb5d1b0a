﻿using System;

namespace iGrest.Core
{
    public partial class Trips
    {
        public virtual bool confirmed { get; set; }

        public virtual Guid id_user { get; set; }

        public virtual bool maxIscritti { get; set; }
    }

    public partial class TripsMaster
    {
        public virtual bool confirmed { get; set; }

        public virtual bool anyConfirmed { get; set; }

        public virtual Guid id_user { get; set; }

        public virtual bool maxIscritti { get; set; }

        public virtual bool subscribedToAll { get; set; }

        public virtual bool subscribedToAny { get; set; }
    }

    public partial class TripsPrice
    {
        public virtual bool confirmed { get; set; }
    }
}
