//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Portale
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Portale()
        {
            this.Grest = new HashSet<Grest>();
        }
    
        public int ID_Portale { get; set; }
        public string Host { get; set; }
        public string Nome { get; set; }
        public string <PERSON>lo { get; set; }
        public string Descrizione { get; set; }
        public string Copertina_<PERSON>lo { get; set; }
        public string Copertina_Messaggio { get; set; }
        public string Link_GooglePlay { get; set; }
        public string Link_AppleStore { get; set; }
        public string Link_Privacy { get; set; }
        public string Link_Adesione { get; set; }
        public string OneSignalAppId { get; set; }
        public string OneSignalApiKey { get; set; }
        public string Url { get; set; }
        public string StripeApiKey { get; set; }
        public string StripeCheckoutWebhookKey { get; set; }
        public string StripeClientId { get; set; }
        public string StripeFormUrl { get; set; }
        public string UrlReset { get; set; }
        public string UrlSignUp { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest> Grest { get; set; }
    }
}
