//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class BalanceReceiptDetails
    {
        public int ID_File { get; set; }
        public System.Guid ID_Balance { get; set; }
        public string TerzoIntermediario_Country { get; set; }
        public string TerzoIntermediario_Piva { get; set; }
        public string TerzoIntermediario_Name { get; set; }
        public string SoggettoEmittente { get; set; }
        public string ID_TipoDocumento { get; set; }
        public decimal AliquotaIva { get; set; }
        public string EsigibilitaIva { get; set; }
        public string RiferimentoNormativo { get; set; }
        public string ID_CondizioniPagamento { get; set; }
        public string ID_ModalitaPagamento { get; set; }
        public Nullable<System.DateTime> DataScadenzaPagamento { get; set; }
        public string IBAN { get; set; }
        public string Progressivo { get; set; }
        public string CodiceDestinatario { get; set; }
        public string ID_FormatoTrasmissione { get; set; }
        public System.DateTime CreatedDate { get; set; }
    
        public virtual BalanceSheets BalanceSheets { get; set; }
        public virtual FE_CondizioniPagamento FE_CondizioniPagamento { get; set; }
        public virtual FE_FormatoTrasmissione FE_FormatoTrasmissione { get; set; }
        public virtual FE_ModalitaPagamento FE_ModalitaPagamento { get; set; }
        public virtual FE_TipoDocumento FE_TipoDocumento { get; set; }
        public virtual Grest_Files Grest_Files { get; set; }
    }
}
