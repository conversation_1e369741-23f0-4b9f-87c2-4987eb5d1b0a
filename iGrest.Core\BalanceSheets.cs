//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class BalanceSheets
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public BalanceSheets()
        {
            this.Grest_Files = new HashSet<Grest_Files>();
            this.Rel_Users_TripsPrice = new HashSet<Rel_Users_TripsPrice>();
            this.BalanceReceiptDetails = new HashSet<BalanceReceiptDetails>();
        }
    
        public System.Guid ID_Balance { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_UserType { get; set; }
        public System.Guid ID_User { get; set; }
        public int ID_Type { get; set; }
        public int ID_Year { get; set; }
        public Nullable<System.DateTime> DataMovimento { get; set; }
        public Nullable<decimal> Importo { get; set; }
        public string Causale { get; set; }
        public bool Removed { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public string Transaction_ID { get; set; }
        public string Transaction_User_Firstname { get; set; }
        public string Transaction_User_Lastname { get; set; }
        public string Transaction_User_Email { get; set; }
        public int ID_TransactionType { get; set; }
        public int ID_PaymentType { get; set; }
        public bool IsPending { get; set; }
        public Nullable<System.Guid> ID_UserCreated { get; set; }
        public Nullable<System.Guid> ID_UserLastEdit { get; set; }
        public Nullable<System.Guid> ID_IncomeStatement { get; set; }
        public byte Status { get; set; }
        public decimal IvaPerc { get; set; }
        public string Hashtags { get; set; }
    
        public virtual BalanceIncomeStatements BalanceIncomeStatements { get; set; }
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        public virtual PaymentType PaymentType { get; set; }
        public virtual TransactionType TransactionType { get; set; }
        public virtual Users Users { get; set; }
        public virtual Users Users1 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files> Grest_Files { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_TripsPrice> Rel_Users_TripsPrice { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceReceiptDetails> BalanceReceiptDetails { get; set; }
    }
}
