//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Dinners
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Dinners()
        {
            this.Mensa = new HashSet<Mensa>();
        }
    
        public int ID_Dinner { get; set; }
        public System.Guid ID_Grest { get; set; }
        public string Name { get; set; }
        public decimal Price { get; set; }
        public bool Removed { get; set; }
        public string Descrizione { get; set; }
        public Nullable<decimal> PriceEdu { get; set; }
        public bool Enabled { get; set; }
    
        public virtual Grest Grest { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Mensa> Mensa { get; set; }
    }
}
