//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Activity
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Activity()
        {
            this.Rel_Users_Activity = new HashSet<Rel_Users_Activity>();
        }
    
        public System.Guid ID_Activity { get; set; }
        public System.Guid ID_Grest { get; set; }
        public int ID_Year { get; set; }
        public string Name { get; set; }
        public Nullable<System.DateTime> StartDate { get; set; }
        public Nullable<System.DateTime> EndDate { get; set; }
        public string Note { get; set; }
        public Nullable<bool> Removed { get; set; }
        public string Days { get; set; }
        public bool Archived { get; set; }
        public string Hashtags { get; set; }
        public Nullable<int> Capacity { get; set; }
    
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Rel_Users_Activity> Rel_Users_Activity { get; set; }
    }
}
