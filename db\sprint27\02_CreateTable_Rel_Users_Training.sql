GO

/****** Object:  Table [dbo].[Rel_Users_Training]    Script Date: 1/15/2024 2:03:18 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Rel_Users_Training](
	[ID_User] [uniqueidentifier] NOT NULL,
	[ID_Training] [uniqueidentifier] NOT NULL,
	[Status] [tinyint] NOT NULL,
	[ChangedStatusDate] [datetime] NULL,
 CONSTRAINT [PK_Rel_Users_Training] PRIMARY KEY CLUSTERED 
(
	[ID_User] ASC,
	[ID_Training] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Rel_Users_Training] ADD  CONSTRAINT [DF_Rel_Users_Training_Status]  DEFAULT ((0)) FOR [Status]
GO

ALTER TABLE [dbo].[Rel_Users_Training]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Users_Training_Trainings] FOREIGN KEY([ID_Training])
REFERENCES [dbo].[Trainings] ([ID_Training])
ON UPDATE CASCADE
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Rel_Users_Training] CHECK CONSTRAINT [FK_Rel_Users_Training_Trainings]
GO

ALTER TABLE [dbo].[Rel_Users_Training]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Users_Training_Users] FOREIGN KEY([ID_User])
REFERENCES [dbo].[Users] ([ID_User])
ON UPDATE CASCADE
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Rel_Users_Training] CHECK CONSTRAINT [FK_Rel_Users_Training_Users]
GO

EXEC sys.sp_addextendedproperty @name=N'MS_Description', @value=N'0 = none | 1 = present | 2 = absent' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'TABLE',@level1name=N'Rel_Users_Training', @level2type=N'COLUMN',@level2name=N'Status'
GO


