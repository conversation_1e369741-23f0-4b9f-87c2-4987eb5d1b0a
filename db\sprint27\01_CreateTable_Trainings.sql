GO

/****** Object:  Table [dbo].[Trainings]    Script Date: 1/15/2024 3:28:03 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Trainings](
	[ID_Training] [uniqueidentifier] NOT NULL,
	[Name] [nvarchar](255) NOT NULL,
	[StartDate] [smalldatetime] NOT NULL,
	[EndDate] [smalldatetime] NOT NULL,
	[Note] [nvarchar](max) NULL,
	[EntryOpen] [bit] NOT NULL,
	[Closed] [bit] NOT NULL,
	[Archived] [bit] NOT NULL,
	[Removed] [bit] NOT NULL,
	[Capacity] [int] NULL,
	[AttachmentFile] [nvarchar](255) NULL,
	[AttachmentText] [nvarchar](255) NULL,
	[Recurring] [bit] NOT NULL,
	[OpenRegStartDate] [datetime] NULL,
	[OpenRegEndDate] [datetime] NULL,
	[CloseRegBeforeHours] [int] NULL,
	[UnsubscribeEnabled] [bit] NOT NULL,
	[RecurringAdvanced] [bit] NOT NULL,
	[Enabled] [bit] NOT NULL,
	[ID_Grest] [uniqueidentifier] NOT NULL,
	[ID_Year] [int] NOT NULL,
	[ID_Headquarter] [int] NULL,
	[ID_Parent] [uniqueidentifier] NULL,
	[LastNotificSendedDate] [datetime] NULL,
 CONSTRAINT [PK_Trainings_1] PRIMARY KEY CLUSTERED 
(
	[ID_Training] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_EntryOpen_1]  DEFAULT ((1)) FOR [EntryOpen]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_Closed_1]  DEFAULT ((0)) FOR [Closed]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_Archived_1]  DEFAULT ((0)) FOR [Archived]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_Removed_1]  DEFAULT ((0)) FOR [Removed]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_Recurring_1]  DEFAULT ((0)) FOR [Recurring]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_UnsubscribeEnabled_1]  DEFAULT ((1)) FOR [UnsubscribeEnabled]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_RecurringAdvanced_1]  DEFAULT ((0)) FOR [RecurringAdvanced]
GO

ALTER TABLE [dbo].[Trainings] ADD  CONSTRAINT [DF_Trainings_Enabled_1]  DEFAULT ((1)) FOR [Enabled]
GO

ALTER TABLE [dbo].[Trainings]  WITH CHECK ADD  CONSTRAINT [FK_Trainings_Grest_Year1] FOREIGN KEY([ID_Year])
REFERENCES [dbo].[Grest_Year] ([ID_Year])
ON UPDATE CASCADE
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Trainings] CHECK CONSTRAINT [FK_Trainings_Grest_Year1]
GO

ALTER TABLE [dbo].[Trainings]  WITH CHECK ADD  CONSTRAINT [FK_Trainings_Grest1] FOREIGN KEY([ID_Grest])
REFERENCES [dbo].[Grest] ([ID_Grest])
ON UPDATE CASCADE
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Trainings] CHECK CONSTRAINT [FK_Trainings_Grest1]
GO

ALTER TABLE [dbo].[Trainings]  WITH CHECK ADD  CONSTRAINT [FK_Trainings_GrestHeadquarters1] FOREIGN KEY([ID_Headquarter])
REFERENCES [dbo].[GrestHeadquarters] ([ID_Headquarter])
GO

ALTER TABLE [dbo].[Trainings] CHECK CONSTRAINT [FK_Trainings_GrestHeadquarters1]
GO

ALTER TABLE [dbo].[Trainings]  WITH CHECK ADD  CONSTRAINT [FK_Trainings_Trainings1] FOREIGN KEY([ID_Parent])
REFERENCES [dbo].[Trainings] ([ID_Training])
GO

ALTER TABLE [dbo].[Trainings] CHECK CONSTRAINT [FK_Trainings_Trainings1]
GO


