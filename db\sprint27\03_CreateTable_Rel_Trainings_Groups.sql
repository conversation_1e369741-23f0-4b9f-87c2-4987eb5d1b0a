USE [iGrest]
GO

/****** Object:  Table [dbo].[Rel_Trainings_Groups]    Script Date: 1/31/2024 4:08:26 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[Rel_Trainings_Groups](
	[ID_Training] [uniqueidentifier] NOT NULL,
	[ID_Group] [uniqueidentifier] NOT NULL,
 CONSTRAINT [PK_Rel_Trainings_Groups] PRIMARY KEY CLUSTERED 
(
	[ID_Training] ASC,
	[ID_Group] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[Rel_Trainings_Groups]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Trainings_Groups_Groups] FOREIGN KEY([ID_Group])
REFERENCES [dbo].[Groups] ([ID_Group])
GO

ALTER TABLE [dbo].[Rel_Trainings_Groups] CHECK CONSTRAINT [FK_Rel_Trainings_Groups_Groups]
GO

ALTER TABLE [dbo].[Rel_Trainings_Groups]  WITH CHECK ADD  CONSTRAINT [FK_Rel_Trainings_Groups_Trainings] FOREIGN KEY([ID_Training])
REFERENCES [dbo].[Trainings] ([ID_Training])
ON UPDATE CASCADE
ON DELETE CASCADE
GO

ALTER TABLE [dbo].[Rel_Trainings_Groups] CHECK CONSTRAINT [FK_Rel_Trainings_Groups_Trainings]
GO


