//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class Grest
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public Grest()
        {
            this.PayPalConfig = new HashSet<PayPalConfig>();
            this.Grest_UserAdditionalDetails = new HashSet<Grest_UserAdditionalDetails>();
            this.GrestFatt = new HashSet<GrestFatt>();
            this.GrestHeadquarters = new HashSet<GrestHeadquarters>();
            this.Order = new HashSet<Order>();
            this.Triage = new HashSet<Triage>();
            this.Activity = new HashSet<Activity>();
            this.Bar = new HashSet<Bar>();
            this.Dinners = new HashSet<Dinners>();
            this.Fornitori = new HashSet<Fornitori>();
            this.Grest_Files = new HashSet<Grest_Files>();
            this.Grest_Iscrizione_Type = new HashSet<Grest_Iscrizione_Type>();
            this.Grest_Progressivo_Ricevuta = new HashSet<Grest_Progressivo_Ricevuta>();
            this.Grest_Year = new HashSet<Grest_Year>();
            this.Groups = new HashSet<Groups>();
            this.InputOutput = new HashSet<InputOutput>();
            this.Listino = new HashSet<Listino>();
            this.Mensa = new HashSet<Mensa>();
            this.News = new HashSet<News>();
            this.Prodotti = new HashSet<Prodotti>();
            this.Users = new HashSet<Users>();
            this.Logs = new HashSet<Logs>();
            this.StripeConfig = new HashSet<StripeConfig>();
            this.BalanceIncomeStatements = new HashSet<BalanceIncomeStatements>();
            this.SatispayConfig = new HashSet<SatispayConfig>();
            this.Grest_Document = new HashSet<Grest_Document>();
            this.BalanceSheets = new HashSet<BalanceSheets>();
            this.Trainings = new HashSet<Trainings>();
            this.SubscriptionAttachments = new HashSet<SubscriptionAttachments>();
            this.Sizes = new HashSet<Sizes>();
            this.ProdottiCategory = new HashSet<ProdottiCategory>();
            this.PrintableObject = new HashSet<PrintableObject>();
            this.BankTransferConfig = new HashSet<BankTransferConfig>();
            this.GrestMenuCustom = new HashSet<GrestMenuCustom>();
            this.Trips = new HashSet<Trips>();
            this.TripsMaster = new HashSet<TripsMaster>();
            this.Messages = new HashSet<Messages>();
        }
    
        public System.Guid ID_Grest { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string Zipcode { get; set; }
        public Nullable<int> ID_Provincia { get; set; }
        public Nullable<int> ID_Comune { get; set; }
        public string Phone { get; set; }
        public string Fax { get; set; }
        public string Email { get; set; }
        public string ReferName { get; set; }
        public string ReferPhone { get; set; }
        public string Note { get; set; }
        public bool Enable { get; set; }
        public decimal CostoMensa { get; set; }
        public Nullable<System.DateTime> LastUpdate { get; set; }
        public Nullable<System.Guid> UserUpdate { get; set; }
        public bool Removed { get; set; }
        public int NumUtenti { get; set; }
        public string PivaCf { get; set; }
        public string NoteRicevuta { get; set; }
        public Nullable<int> Capacity { get; set; }
        public string Logo { get; set; }
        public Nullable<bool> ScaricoAutomatico { get; set; }
        public string Codice { get; set; }
        public string NoteIscrizione { get; set; }
        public Nullable<bool> IscrizioneGiteGratuita { get; set; }
        public Nullable<bool> InvioMailAttivazioneUtenti { get; set; }
        public Nullable<bool> EnableSwitchUtenti { get; set; }
        public string Privacy { get; set; }
        public string LicenseType { get; set; }
        public string PdfCustom { get; set; }
        public string TestoPdfCustom { get; set; }
        public Nullable<bool> AbilitaNotificaIscrizone { get; set; }
        public Nullable<bool> AbilitaNotificaInOut { get; set; }
        public Nullable<bool> AbilitaNotificaMensa { get; set; }
        public Nullable<bool> AbilitaNotificaMovimenti { get; set; }
        public Nullable<bool> AbilitaNotificaMessaggi { get; set; }
        public Nullable<bool> AbilitaNotificaDocumenti { get; set; }
        public Nullable<bool> AbilitaNotificaUtenteDaAttivare { get; set; }
        public bool AbilitaNotifiche { get; set; }
        public Nullable<bool> IscrAssicurazione { get; set; }
        public Nullable<bool> IscrLimiteBar { get; set; }
        public Nullable<bool> IscrAvatar { get; set; }
        public Nullable<bool> IscrDatiFiscali { get; set; }
        public Nullable<bool> IscrSettimane { get; set; }
        public Nullable<int> IscrNumeroSettimane { get; set; }
        public Nullable<bool> IscrTaglia { get; set; }
        public Nullable<bool> IscrFreqCosti { get; set; }
        public Nullable<bool> IscrClasseFrequentata { get; set; }
        public Nullable<bool> IscrElencoDelegati { get; set; }
        public Nullable<bool> Catechismo { get; set; }
        public int NumEducatori { get; set; }
        public string FirmaFatturazione { get; set; }
        public string TipoGrest { get; set; }
        public Nullable<bool> IscrAPagamento { get; set; }
        public Nullable<decimal> IscrQuota { get; set; }
        public bool IscrAzzeraCrediti { get; set; }
        public bool IscrRecuperaUser { get; set; }
        public Nullable<decimal> IscrQuotaSpesa { get; set; }
        public Nullable<System.DateTime> DataScadenza { get; set; }
        public Nullable<bool> IscrHeadquarter { get; set; }
        public string IscrForm { get; set; }
        public Nullable<bool> IscrSchedaInfoaggiuntive { get; set; }
        public Nullable<bool> IscrSchedaGenitori { get; set; }
        public string TestoPrivacy { get; set; }
        public string PdfCustom_2 { get; set; }
        public string TestoPdfCustom_2 { get; set; }
        public bool AbilitaFlagAbile { get; set; }
        public byte BarcodeChars { get; set; }
        public bool AbilitaModificaNote { get; set; }
        public Nullable<int> ID_Portale { get; set; }
        public string NotificaIn { get; set; }
        public string NotificaOut { get; set; }
        public bool TripIscrEducator { get; set; }
        public Nullable<System.DateTime> DefaultDateFrom { get; set; }
        public string PaymentSystem { get; set; }
        public bool HideEconomicCard { get; set; }
        public bool ConsumoBarSenzaCredito { get; set; }
        public string CausaleRicarica { get; set; }
        public bool IscrClassName { get; set; }
        public bool MultiDailyFee { get; set; }
        public bool IscrCaricaDocumento { get; set; }
        public bool GestioneIva { get; set; }
        public bool ControlloAccessoEdu { get; set; }
        public bool TripIscrDoorman { get; set; }
        public string Hashtags { get; set; }
        public bool AbilitaUploadDocUser { get; set; }
    
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PayPalConfig> PayPalConfig { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_UserAdditionalDetails> Grest_UserAdditionalDetails { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<GrestFatt> GrestFatt { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<GrestHeadquarters> GrestHeadquarters { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Order> Order { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Triage> Triage { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Activity> Activity { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Bar> Bar { get; set; }
        public virtual Comuni Comuni { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Dinners> Dinners { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Fornitori> Fornitori { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Files> Grest_Files { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Iscrizione_Type> Grest_Iscrizione_Type { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Progressivo_Ricevuta> Grest_Progressivo_Ricevuta { get; set; }
        public virtual Province Province { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Year> Grest_Year { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Groups> Groups { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<InputOutput> InputOutput { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Listino> Listino { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Mensa> Mensa { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<News> News { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Prodotti> Prodotti { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Users> Users { get; set; }
        public virtual Portale Portale { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Logs> Logs { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<StripeConfig> StripeConfig { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceIncomeStatements> BalanceIncomeStatements { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SatispayConfig> SatispayConfig { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Grest_Document> Grest_Document { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BalanceSheets> BalanceSheets { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trainings> Trainings { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<SubscriptionAttachments> SubscriptionAttachments { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Sizes> Sizes { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<ProdottiCategory> ProdottiCategory { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<PrintableObject> PrintableObject { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<BankTransferConfig> BankTransferConfig { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<GrestMenuCustom> GrestMenuCustom { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Trips> Trips { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<TripsMaster> TripsMaster { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Messages> Messages { get; set; }
    }
}
