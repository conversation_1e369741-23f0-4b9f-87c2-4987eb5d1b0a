﻿using iGrest.Core;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Web.Script.Serialization;

namespace iGrest.Jobs.Services
{
	public class OneSignalSender
	{
		public OneSignalSender()
		{
			//
			// TODO: Add constructor logic here
			//
		}
		public List<string> GetAllAndParentPlayers(string id_user)
		{
			var parentPlayers = new List<string>();

			var parents = getRelUserChild(id_user);
			if (parents != null)
			{
				foreach (var parent in parents)
				{
					var parentP = GetAllPlayers(parent.ToString());

					if (parentP != null)
						parentPlayers.AddRange(parentP);
				}
			}

			var players = GetAllPlayers(id_user);
			if (parentPlayers != null)
				players.AddRange(parentPlayers);

			return players;
		}

		public List<string> GetAllPlayers(string id_user)
		{
			var result = new List<string>();

			var items = new List<REL_Users_NotificPlayers>();

			var user = new Guid(id_user);

			using (var db = new iGrestEntities())
			{
				var o = db.REL_Users_NotificPlayers.AsQueryable();

				o = o.Where(x => x.ID_User == user);

				items = o.ToList();
			}

			foreach (var item in items)
			{
				result.Add(item.ID_Player);
			}

			return result;
		}

		private List<Guid> getRelUserChild(string id_child)
		{
			var result = new List<Guid>();

			var items = new List<Users>();

			var child = new Guid(id_child);

			using (var db = new iGrestEntities())
			{
				var o = db.Users
					.Include("Rel_Users_Year")
					.AsQueryable();

				o = o.Where(x =>
					x.Rel_Users_Year.Any(y =>
						!y.Removed &&
						db.Grest_Year.Where(z => !z.Removed && z.ID_Grest == x.ID_Grest).OrderByDescending(z => z.ID_Year).FirstOrDefault().ID_Year == y.ID_Year)
				);

				o = o.Where(x => x.Rel_Users.Any(y => y.ID_Parent == x.ID_User && y.ID_Child == child));

				o = o.OrderBy(x => x.Lastname);

				items = o.ToList();
			}

			foreach (var item in items)
			{
				result.Add(item.ID_User);
			}

			return result;
		}

		public void SendNotific(Portale portale, string[] player_ids, object contents, object[] buttons, string url = "")
		{
			player_ids = player_ids.Where(x => x.ToLower() != "undefined").ToArray();

			var appId = portale.OneSignalAppId;
			var restApiKey = portale.OneSignalApiKey;

			var take = 50;
			for (var skip = 0; skip < player_ids.Length; skip += take)
			{
				var players = player_ids.Skip(skip).Take(take).ToArray();

				var serializer = new JavaScriptSerializer();
				var obj = new
				{
					app_id = appId,
					contents = contents,
					web_buttons = buttons,
					include_player_ids = players,
					data = new { targetUrl = "https://" + portale.Host + "/" + url.TrimStart('/') }
				};

				var param = serializer.Serialize(obj);
				byte[] byteArray = Encoding.UTF8.GetBytes(param);

				try
				{
					ServicePointManager.Expect100Continue = true;
					ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072; //| SecurityProtocolType.Ssl3;
					ServicePointManager.ServerCertificateValidationCallback =
						delegate (object s, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) { return true; };

					var request = WebRequest.Create("https://onesignal.com/api/v1/notifications") as HttpWebRequest;

					request.KeepAlive = true;
					request.Method = "POST";
					request.ContentType = "application/json; charset=utf-8";

					request.Headers.Add("authorization", "Basic " + restApiKey);

					using (var writer = request.GetRequestStream())
					{
						writer.Write(byteArray, 0, byteArray.Length);
					}

					using (var response = request.GetResponse() as HttpWebResponse)
					{
						using (var reader = new StreamReader(response.GetResponseStream()))
						{
							var responseContent = reader.ReadToEnd();
						}
					}
				}
				catch (WebException ex)
				{
					//unhandled exception

					using (var db = new iGrestEntities())
					{
						try
						{
							Log l = new Log();
							l.DTINSERT = DateTime.Now;
							l.innerexception = ex.InnerException != null ? ex.InnerException.ToString() : "";
							l.message = ex.Message;
							l.pagina = ("Invio notifica - dest: " + string.Join(";", players)).Substring(100);
							l.source = ex.Source;
							l.stacktrace = ex.StackTrace;
							l.userid = "";
							db.Log.Add(l);
							db.SaveChanges();
						}
						catch
						{

						}
					}
				}

				Thread.Sleep(400);
			}
		}
	}
}