﻿using FluentFTP;
using Microsoft.Extensions.Logging;
using Serilog;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows;
using System.Windows.Controls.Primitives;

namespace iGrest.Utils
{
	/// <summary>
	/// Interaction logic for DeployWindow.xaml
	/// </summary>
	public partial class DeployWindow : Window
	{
		public DeployWindow()
		{
			InitializeComponent();

			var loggerFactory = new LoggerFactory();

			var loggerConfig = new LoggerConfiguration()
				.WriteTo.File("logs\\iGrest_utils.log", rollingInterval: RollingInterval.Day)
				.MinimumLevel.Warning()
				.CreateLogger();

			_logger = loggerFactory.CreateLogger<DeployWindow>();

			loggerFactory.AddSerilog(loggerConfig);
		}

		private CancellationTokenSource _cancellationToken = new CancellationTokenSource();

		private ILogger<DeployWindow> _logger;

		private static string FtpIP
		{
			get
			{
				return ConfigurationManager.AppSettings["ftp.ip"];
			}
		}

		private static string FtpUsername
		{
			get
			{
				return ConfigurationManager.AppSettings["ftp.username"];
			}
		}

		private static string FtpPassword
		{
			get
			{
				return ConfigurationManager.AppSettings["ftp.password"];
			}
		}

        private static string FtpProdIP
        {
            get
            {
                return ConfigurationManager.AppSettings["ftp.prodIp"];
            }
        }

        private static string FtpProdUsername
        {
            get
            {
                return ConfigurationManager.AppSettings["ftp.prodUsername"];
            }
        }

        private static string FtpProdPassword
        {
            get
            {
                return ConfigurationManager.AppSettings["ftp.prodPassword"];
            }
        }

        private static string FtpProdFolder
		{
			get
			{
				return ConfigurationManager.AppSettings["ftp.prodFolder"];
			}
		}

		private static string FtpStagingFolder
		{
			get
			{
				return ConfigurationManager.AppSettings["ftp.stagingFolder"];
			}
		}

		private static string UrlProd
		{
			get
			{
				return ConfigurationManager.AppSettings["url.prod"];
			}
		}

		private static string UrlStaging
		{
			get
			{
				return ConfigurationManager.AppSettings["url.staging"];
			}
		}

		private static string LocalFolder
		{
			get
			{
				return ConfigurationManager.AppSettings["local.folder"];
			}
		}

		/// <summary>
		/// https://github.com/robinrodricks/FluentFTP/wiki/Quick-Start-Example
		/// </summary>
		/// <param name="sender"></param>
		/// <param name="e"></param>
		private async void btnStart_Click(object sender, RoutedEventArgs e)
		{
			Application.Current.Dispatcher.Invoke(() =>
			{
				btnStart.Visibility = Visibility.Collapsed;
				btnStop.Visibility = Visibility.Visible;
			});

			// create an FTP client and specify the host, username and password
			// (delete the credentials to use the "anonymous" account)
			var client = new AsyncFtpClient(FtpIP, FtpUsername, FtpPassword, 21, new FtpConfig
			{
				ValidateAnyCertificate = true
			});

			try
			{
				logInfo("START DEPLOY");

				var env = "STAGING (" + UrlStaging + ")";
				var basePath = FtpStagingFolder;
				if (rbProd.IsChecked.HasValue && rbProd.IsChecked.Value)
				{
					env = "PRODUCTION(" + UrlProd + ")";
					basePath = FtpProdFolder;

					client = new AsyncFtpClient(FtpProdIP, FtpProdUsername, FtpProdPassword, 21, new FtpConfig
                    {
                        ValidateAnyCertificate = true
                    });
                }

				logInfo("ENVIRONMENT=" + env);

				var rr = MessageBox.Show("Sei sicuro di voler deployare in " + env + "?", "ATTENZIONE", MessageBoxButton.YesNo, MessageBoxImage.Question);

				if (rr == MessageBoxResult.No)
					return;

				// connect to the server and automatically detect working FTP settings
				await client.AutoConnect();

				var adminFiles = getAdminFile();

				logInfo("ADMIN > " + adminFiles.Count + " files");

				foreach (var f in adminFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var ajaxFiles = getAjaxFile();

				logInfo("AJAX > " + ajaxFiles.Count + " files");

				foreach (var f in ajaxFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var appCodeFiles = getAppCodeFile();

				logInfo("APP_CODE > " + appCodeFiles.Count + " files");

				foreach (var f in appCodeFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var cssFiles = getAssetsCss();

				logInfo("CSS > " + cssFiles.Count + " files");

				foreach (var f in cssFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var jsFiles = getAssetsJs();

				logInfo("JS > " + jsFiles.Count + " files");

				foreach (var f in jsFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				if (chkChangeDB.IsChecked.HasValue && chkChangeDB.IsChecked.Value)
				{
					var binFiles = getBinDatabaseDll();

					logInfo("BIN > " + binFiles.Count + " files");

					foreach (var f in binFiles)
					{
						var remoteF = remotePath(basePath, f);

						// upload a file and retry 3 times before giving up
						client.Config.RetryAttempts = 3;

						var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

						appendResult(r, remoteF);

						if (_cancellationToken.IsCancellationRequested)
							return;
					}
				}

				var customFiles = getCustomSubscription();

				logInfo("CUSTOM > " + customFiles.Count + " files");

				foreach (var f in customFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var subscriptionFiles = getSubscription();

				logInfo("SUBSCRIPTION > " + subscriptionFiles.Count + " files");

				foreach (var f in subscriptionFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}

				var additionalFiles = getAdditional();

				logInfo("ADDITIONAL > " + additionalFiles.Count + " files");

				foreach (var f in additionalFiles)
				{
					var remoteF = remotePath(basePath, f);

					// upload a file and retry 3 times before giving up
					client.Config.RetryAttempts = 3;

					var r = await client.UploadFile(f.FullName, remoteF, FtpRemoteExists.Overwrite, false, FtpVerify.Retry, token: _cancellationToken.Token);

					appendResult(r, remoteF);

					if (_cancellationToken.IsCancellationRequested)
						return;
				}
			}
			catch (Exception ex)
			{
				logError("Errore durante il deploy - ex: " + ex);
			}
			finally
			{
				// disconnect! good bye!
				await client.Disconnect();
				client.Dispose();

				btnStop_Click(sender, e);

				logInfo("END DEPLOY");
			}
		}

		private void btnStop_Click(object sender, RoutedEventArgs e)
		{
			Application.Current.Dispatcher.Invoke(() =>
			{
				btnStart.Visibility = Visibility.Visible;
				btnStop.Visibility = Visibility.Collapsed;
			});

			_cancellationToken.Cancel();
		}

		private void appendResult(FtpStatus r, string remoteF)
		{
			string msg;
			if (r == FtpStatus.Success)
			{
				msg = remoteF + " > SUCCESS";
			}
			else if (r == FtpStatus.Skipped)
			{
				msg = remoteF + " > SKIPPED";
			}
			else
			{
				msg = remoteF + " > FAILED";
			}

			logInfo(msg);
		}

		private void logInfo(string msg)
		{
			_logger.LogInformation(msg);

			Application.Current.Dispatcher.Invoke(() =>
			{
				txbResult.Text += msg + "\r\r";
			});			
		}

		private void logError(string msg)
		{
			_logger.LogError(msg);

			Application.Current.Dispatcher.Invoke(() =>
			{
				txbResult.Text += "[ERROR] " + msg + "\r\n";
			});
		}

		private void logWarning(string msg)
		{
			_logger.LogWarning(msg);

			Application.Current.Dispatcher.Invoke(() =>
			{
				txbResult.Text += "[Warning] " + msg + "\r\n";
			});
		}

		private List<FileInfo> getAdminFile()
		{
			var dir = Path.Combine(LocalFolder, "admin");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles();

			return files
				.Where(x => x.Name.ToLower() != "web.config")
				.ToList();
		}

		private List<FileInfo> getAjaxFile()
		{
			var dir = Path.Combine(LocalFolder, "ajax");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles();

			return files
				.ToList();
		}

		private List<FileInfo> getAppCodeFile()
		{
			var dir = Path.Combine(LocalFolder, "App_Code");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles("*", SearchOption.AllDirectories);

			return files
				.Where(x => !x.DirectoryName.ToLower().Contains("satispay"))
				.ToList();
		}

		private List<FileInfo> getAssetsCss()
		{
			var dir = Path.Combine(LocalFolder, "assets", "app", "css");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles("*", SearchOption.AllDirectories);

			return files
				.ToList();
		}

		private List<FileInfo> getAssetsJs()
		{
			var dir = Path.Combine(LocalFolder, "assets", "app", "js");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles("*", SearchOption.AllDirectories);

			return files
				.ToList();
		}

		private List<FileInfo> getBinDatabaseDll()
		{
			var dir = Path.Combine(LocalFolder, "Bin");

			return new List<FileInfo> {
				new FileInfo(Path.Combine(dir, "iGrest.Core.dll")),
			};
		}

		private List<FileInfo> getCustomSubscription()
		{
			var dir = Path.Combine(LocalFolder, "custom");

			var dirInfo = new DirectoryInfo(dir);

			var files = dirInfo.GetFiles("*", SearchOption.AllDirectories);

			return files
				.ToList();
		}

		private List<FileInfo> getSubscription()
		{
			var dir = Path.Combine(LocalFolder);

			return new List<FileInfo> {
				new FileInfo(Path.Combine(dir, "signup.aspx")),
				new FileInfo(Path.Combine(dir, "signup.aspx.cs")),
				new FileInfo(Path.Combine(dir, "iscrizione.aspx")),
				new FileInfo(Path.Combine(dir, "iscrizione_ospiti.aspx")),
				new FileInfo(Path.Combine(dir, "iscrizione_avvenuta_correttamente.aspx")),
			};
		}

		private List<FileInfo> getAdditional()
		{
			var result = new List<FileInfo>();

			if (string.IsNullOrWhiteSpace(txtAdditionalFiles.Text))
				return result;

			var dir = Path.Combine(LocalFolder);

			var arrFiles = txtAdditionalFiles.Text.Split(',');

			foreach (var file in arrFiles)
			{
				var fn = file.Trim();
				result.Add(new FileInfo(Path.Combine(dir, fn)));
			}

			return result;
		}

		private string remotePath(string basePath, FileInfo f)
		{
			var fn = f.FullName.Replace(LocalFolder, string.Empty).Replace("\\", "/");

			return basePath + fn;
		}

        private void txbResult_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
			sv.ScrollToEnd();
        }
    }
}
