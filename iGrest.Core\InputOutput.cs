//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace iGrest.Core
{
    using System;
    using System.Collections.Generic;
    
    public partial class InputOutput
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public InputOutput()
        {
            this.Triage_InputOutput = new HashSet<Triage_InputOutput>();
        }
    
        public System.Guid ID_Input { get; set; }
        public Nullable<System.Guid> ID_Grest { get; set; }
        public Nullable<int> ID_Year { get; set; }
        public Nullable<System.Guid> ID_User { get; set; }
        public Nullable<System.DateTime> DateIn { get; set; }
        public Nullable<System.DateTime> DateOut { get; set; }
        public string Barcode { get; set; }
        public string NoteIn { get; set; }
        public string NoteOut { get; set; }
        public Nullable<int> ID_Headquarter { get; set; }
        public Nullable<System.Guid> ID_UserCreated { get; set; }
    
        public virtual GrestHeadquarters GrestHeadquarters { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<Triage_InputOutput> Triage_InputOutput { get; set; }
        public virtual Grest Grest { get; set; }
        public virtual Grest_Year Grest_Year { get; set; }
        public virtual Users Users { get; set; }
        public virtual Users Users1 { get; set; }
    }
}
